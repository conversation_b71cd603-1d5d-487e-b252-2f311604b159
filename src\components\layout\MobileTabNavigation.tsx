'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Upload, 
  History, 
  User, 
  Eye,
  Settings
} from 'lucide-react';

interface TabItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

const tabItems: TabItem[] = [
  {
    name: 'Home',
    href: '/dashboard',
    icon: Home,
    label: 'Home'
  },
  {
    name: 'Analysis',
    href: '/dashboard/new-analysis',
    icon: Upload,
    label: 'Analysis'
  },
  {
    name: 'Model',
    href: '/model-viewer',
    icon: Eye,
    label: 'Model'
  },
  {
    name: 'History',
    href: '/dashboard/history',
    icon: History,
    label: 'History'
  },
  {
    name: 'Profile',
    href: '/dashboard/profile',
    icon: User,
    label: 'Profile'
  }
];

export const MobileTabNavigation: React.FC = () => {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard';
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 lg:hidden">
      {/* Tab Navigation */}
      <div className="bg-black/90 backdrop-blur-xl border-t border-white/10">
        <div className="grid grid-cols-5 h-20 safe-area-pb">
          {tabItems.map((item) => {
            const IconComponent = item.icon;
            const active = isActive(item.href);
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={`flex flex-col items-center justify-center py-2 px-1 transition-all duration-300 ${
                  active
                    ? 'text-white'
                    : 'text-gray-400 hover:text-gray-200'
                }`}
              >
                <div className={`relative p-2 rounded-2xl transition-all duration-300 ${
                  active 
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg scale-110' 
                    : 'hover:bg-white/10'
                }`}>
                  <IconComponent className={`w-6 h-6 ${active ? 'text-white' : ''}`} />
                  
                  {/* Active indicator dot */}
                  {active && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-black animate-pulse"></div>
                  )}
                </div>
                
                <span className={`text-xs font-medium mt-1 transition-colors duration-300 ${
                  active ? 'text-white' : 'text-gray-400'
                }`}>
                  {item.label}
                </span>
                
                {/* Active indicator line */}
                {active && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-t-full"></div>
                )}
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};
