'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { MaleModel } from '@/components/3d/models/MaleModel';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
  outfitName: string;
}

export default function ThreeDModelPage() {
  const [colorCombinations, setColorCombinations] = useState<ColorCombination[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load color recommendations when component mounts
  useEffect(() => {
    loadColorRecommendations();
  }, []);

  const loadColorRecommendations = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🎨 Loading color recommendations from API...');

      // First try the API
      try {
        const response = await fetch('https://faceapp-ttwh.onrender.com/api/face/recommendations/latest', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ API Response:', data);

          if (data && data.recommendations && data.recommendations.length > 0) {
            const combinations: ColorCombination[] = data.recommendations.map((outfit: any, index: number) => ({
              shirt: outfit.shirt?.hex || '#3498db',
              pants: outfit.pants?.hex || '#2c3e50',
              shoes: outfit.shoes?.hex || '#000000',
              outfitName: outfit.outfitName || `Style ${index + 1}`,
            }));

            setColorCombinations(combinations);
            console.log('🎨 Converted combinations:', combinations);
            return;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API failed, using fallback colors');
      }

      // Fallback: Use hardcoded test colors
      console.log('🎨 Using fallback test colors');
      const testCombinations: ColorCombination[] = [
        {
          shirt: '#ff0000',
          pants: '#00ff00',
          shoes: '#0000ff',
          outfitName: 'Test Red/Green/Blue'
        },
        {
          shirt: '#ffff00',
          pants: '#ff00ff',
          shoes: '#00ffff',
          outfitName: 'Test Yellow/Magenta/Cyan'
        },
        {
          shirt: '#8B4513',
          pants: '#2F4F4F',
          shoes: '#000000',
          outfitName: 'Test Brown/Gray/Black'
        }
      ];

      setColorCombinations(testCombinations);
      console.log('🎨 Set test combinations:', testCombinations);

    } catch (err: any) {
      console.error('❌ Error loading recommendations:', err);
      setError(err.message || 'Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedCombination = colorCombinations[selectedIndex] || null;

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900 relative">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
            🎨 AI Color Recommendations Test
          </h1>
          <p className="text-purple-200">
            Testing direct color application to 3D model
          </p>
          {selectedCombination && (
            <div className="mt-4 p-4 bg-black/30 rounded-lg">
              <p className="text-white">
                <strong>Current:</strong> {selectedCombination.outfitName}
              </p>
              <p className="text-white/80 text-sm">
                Shirt: {selectedCombination.shirt} |
                Pants: {selectedCombination.pants} |
                Shoes: {selectedCombination.shoes}
              </p>
              <div className="mt-2 flex gap-2">
                <button
                  onClick={() => {
                    const testColors = {
                      shirt: '#ff0000',
                      pants: '#00ff00',
                      shoes: '#0000ff',
                      outfitName: 'DEBUG: Red/Green/Blue'
                    };
                    setColorCombinations([testColors, ...colorCombinations]);
                    setSelectedIndex(0);
                    console.log('🧪 Applied debug colors:', testColors);
                  }}
                  className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                >
                  🧪 Test RGB
                </button>
                <button
                  onClick={() => {
                    const extremeColors = {
                      shirt: '#ffff00',
                      pants: '#ff00ff',
                      shoes: '#00ffff',
                      outfitName: 'EXTREME: Yellow/Magenta/Cyan'
                    };
                    setColorCombinations([extremeColors, ...colorCombinations]);
                    setSelectedIndex(0);
                    console.log('🧪 Applied extreme colors:', extremeColors);
                  }}
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                >
                  ⚡ Extreme
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 3D Canvas */}
      <div className="w-full h-screen">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          <MaleModel
            colorCombination={selectedCombination}
            enableColorSync={true}
          />

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
          />
        </Canvas>
      </div>

      {/* Color Combinations Panel */}
      <div className="absolute right-0 top-0 bottom-0 w-80 bg-black/40 backdrop-blur-2xl border-l border-white/10 flex flex-col">
        <div className="p-6 flex-shrink-0">
          <h3 className="text-xl font-bold text-white mb-4">Color Combinations</h3>

          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Loading...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-400 mb-4">{error}</p>
              <button
                onClick={loadColorRecommendations}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
              >
                Retry
              </button>
            </div>
          )}


        </div>

        {/* Scrollable content area */}
        <div className="flex-1 overflow-y-auto px-6 pb-6">
          {colorCombinations.length > 0 && (
            <div className="space-y-3">
              {colorCombinations.map((combination, index) => (
                <div
                  key={index}
                  onClick={() => {
                    setSelectedIndex(index);
                    console.log('🎨 Selected combination:', combination);
                  }}
                  className={`p-4 rounded-lg cursor-pointer transition-all ${
                    selectedIndex === index
                      ? 'bg-purple-500/30 border-2 border-purple-400'
                      : 'bg-white/10 hover:bg-white/20 border-2 border-transparent'
                  }`}
                >
                  <h4 className="text-white font-semibold mb-2">{combination.outfitName}</h4>

                  <div className="flex gap-2 mb-2">
                    <div className="flex flex-col items-center">
                      <div
                        className="w-8 h-8 rounded border border-white/30"
                        style={{ backgroundColor: combination.shirt }}
                      ></div>
                      <span className="text-xs text-white/70">Shirt</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div
                        className="w-8 h-8 rounded border border-white/30"
                        style={{ backgroundColor: combination.pants }}
                      ></div>
                      <span className="text-xs text-white/70">Pants</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div
                        className="w-8 h-8 rounded border border-white/30"
                        style={{ backgroundColor: combination.shoes }}
                      ></div>
                      <span className="text-xs text-white/70">Shoes</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
