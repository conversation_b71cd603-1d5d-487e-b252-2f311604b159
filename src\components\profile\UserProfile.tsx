'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { authAPI } from '@/lib/api';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { validateEmail } from '@/lib/utils';
import { 
  User, 
  Mail, 
  Users, 
  Edit3, 
  Save, 
  X,
  Shield,
  Bell,
  Palette,
  Download
} from 'lucide-react';
import toast from 'react-hot-toast';

export const UserProfile: React.FC = () => {
  const { user, refreshUser } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);
  const [fetchingProfile, setFetchingProfile] = useState(true);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    gender: user?.gender || 'prefer_not_to_say',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch user profile data on component mount
  useEffect(() => {
    fetchUserProfile();
  }, []);

  // Update form data when profile data is fetched
  useEffect(() => {
    console.log('🔄 ProfileData state changed:', profileData);
    if (profileData) {
      console.log('✅ Setting form data with profile data:', profileData);
      setFormData({
        name: profileData.name || '',
        email: profileData.email || '',
        gender: profileData.gender || 'prefer_not_to_say'
      });
    } else {
      console.log('❌ ProfileData is null/undefined');
    }
  }, [profileData]);

  const fetchUserProfile = async () => {
    try {
      setFetchingProfile(true);
      console.log('🔄 Fetching user profile from API...');

      const response = await authAPI.getProfile();
      console.log('🔍 Full Profile API response:', response);
      console.log('🔍 Response data structure:', response.data);

      if (response.success && response.data) {
        console.log('✅ Profile API response by utkarsh singh:', response);

        // Handle the actual nested response structure from console logs
        const responseData = response.data as any;
        let userData = null;

        console.log('🔍 Checking responseData structure:', responseData);
        console.log('🔍 responseData.data:', responseData.data);
        console.log('🔍 responseData.user:', responseData.user);

        // Based on console logs: response.data.data.user
        if (responseData.data && responseData.data.user) {
          userData = responseData.data.user;
          console.log('✅ Extracted user data from nested structure (data.data.user):', userData);
        }
        // Fallback: response.data.user
        else if (responseData.user) {
          userData = responseData.user;
          console.log('✅ Extracted user data from direct structure (data.user):', userData);
        }
        // Fallback: response.data directly
        else if (responseData.id) {
          userData = responseData;
          console.log('✅ Using response.data directly:', userData);
        }
        // Last resort: check if responseData itself has the user properties
        else if (responseData.name || responseData.email) {
          userData = responseData;
          console.log('✅ Using responseData as user data:', userData);
        }

        if (userData) {
          setProfileData(userData);
          console.log('✅ Profile data set successfully in state:', userData);
          console.log('✅ User name:', userData.name);
          console.log('✅ User email:', userData.email);
        } else {
          console.error('❌ Could not extract user data from response structure');
          console.error('❌ Full response.data:', response.data);
          console.error('❌ Available keys in response.data:', Object.keys(responseData));
        }
      } else {
        console.error('❌ Profile API failed:', response);
      }
    } catch (error: any) {
      console.error('💥 Failed to fetch profile:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.response?.status === 401) {
        console.error('Authentication failed - redirecting to login');
      } else {
        console.error('Failed to load profile');
      }
    } finally {
      setFetchingProfile(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setIsLoading(true);
      // TODO: Implement profile update API call
      // await authAPI.updateProfile(formData);
      
      await refreshUser();
      setIsEditing(false);
      toast.success('Profile updated successfully!');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
      gender: user?.gender || 'prefer_not_to_say',
    });
    setErrors({});
    setIsEditing(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Show loading state while fetching profile
  if (fetchingProfile) {
    return (
      <div className="space-y-6">
        <div className="card">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Profile</h3>
            <p className="text-gray-600">Fetching your profile data from API...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if no profile data
  if (!profileData) {
    return (
      <div className="space-y-6">
        <div className="card">
          <div className="text-center py-8">
            <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Profile Data</h3>
            <p className="text-gray-600">Failed to load profile. Please try again.</p>
            <button
              onClick={fetchUserProfile}
              className="mt-4 btn-primary"
            >
              Retry Loading Profile
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Debug Info */}
      <div className="card bg-yellow-50 border-yellow-200">
        <h3 className="text-lg font-bold text-yellow-800 mb-4">🔍 Debug Information</h3>
        <div className="space-y-2 text-sm">
          <div><strong>API Endpoint:</strong> https://faceapp-ttwh.onrender.com/api/auth/me</div>
          <div><strong>Fetching Profile:</strong> {fetchingProfile ? 'Yes 🔄' : 'No'}</div>
          <div><strong>Profile Data State:</strong> {profileData ? 'Loaded ✅' : 'Not loaded ❌'}</div>
          <div><strong>Profile Data Type:</strong> {typeof profileData}</div>
          <div><strong>Profile Data Keys:</strong> {profileData ? Object.keys(profileData).join(', ') : 'None'}</div>
          <div><strong>User ID:</strong> {profileData?.id || 'N/A'}</div>
          <div><strong>Name:</strong> {profileData?.name || 'N/A'}</div>
          <div><strong>Email:</strong> {profileData?.email || 'N/A'}</div>
          <div><strong>Gender:</strong> {profileData?.gender || 'N/A'}</div>
          <div><strong>Email Verified:</strong> {profileData?.isEmailVerified ? 'Yes ✅' : 'No ❌'}</div>
          <div><strong>Created At:</strong> {profileData?.createdAt || 'N/A'}</div>
          <div><strong>Last Login:</strong> {profileData?.lastLogin || 'N/A'}</div>
          <div><strong>Raw Profile Data:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-32">
              {profileData ? JSON.stringify(profileData, null, 2) : 'null'}
            </pre>
          </div>
        </div>
        <button
          onClick={fetchUserProfile}
          className="mt-4 btn-secondary text-sm"
        >
          🔄 Refresh Profile Data
        </button>
      </div>

      {/* Profile Header */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Profile Settings</h2>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Edit3 className="w-4 h-4" />
              <span>Edit Profile</span>
            </button>
          ) : (
            <div className="flex space-x-2">
              <LoadingButton
                onClick={handleSave}
                isLoading={isLoading}
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </LoadingButton>
              <button
                onClick={handleCancel}
                disabled={isLoading}
                className="btn-secondary flex items-center space-x-2"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
            </div>
          )}
        </div>

        {/* Profile Picture */}
        <div className="flex items-center space-x-6 mb-6">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="w-10 h-10 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">{profileData?.name || 'Loading...'}</h3>
            <p className="text-gray-600">{profileData?.email || 'Loading...'}</p>
            {profileData?.isEmailVerified && (
              <div className="flex items-center gap-1 mt-1">
                <Shield className="w-3 h-3 text-green-500" />
                <span className="text-xs text-green-600 font-medium">Email Verified</span>
              </div>
            )}
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium mt-1">
              Change Photo
            </button>
          </div>
        </div>

        {/* Profile Form */}
        <div className="space-y-4">
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            {isEditing ? (
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`input-field pl-10 ${errors.name ? 'border-red-500' : ''}`}
                  placeholder="Enter your full name"
                />
              </div>
            ) : (
              <p className="text-gray-900 font-medium">{user?.name}</p>
            )}
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            {isEditing ? (
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`input-field pl-10 ${errors.email ? 'border-red-500' : ''}`}
                  placeholder="Enter your email"
                />
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <p className="text-gray-900 font-medium">{user?.email}</p>
                {user?.isEmailVerified && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                    Verified
                  </span>
                )}
              </div>
            )}
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>

          {/* Gender */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Gender
            </label>
            {isEditing ? (
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="input-field pl-10"
                >
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                  <option value="prefer_not_to_say">Prefer not to say</option>
                </select>
              </div>
            ) : (
              <p className="text-gray-900 font-medium capitalize">
                {user?.gender?.replace('_', ' ')}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Bell className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium text-gray-900">Email Notifications</p>
                <p className="text-sm text-gray-600">Receive updates about new features</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Palette className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium text-gray-900">Save Color Preferences</p>
                <p className="text-sm text-gray-600">Remember your favorite color combinations</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" className="sr-only peer" defaultChecked />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Account Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Account</h3>
        <div className="space-y-3">
          <button className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 rounded-lg transition-colors duration-200">
            <div className="flex items-center space-x-3">
              <Download className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium text-gray-900">Export Data</p>
                <p className="text-sm text-gray-600">Download all your analysis data</p>
              </div>
            </div>
            <span className="text-gray-400">→</span>
          </button>

          <button className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 rounded-lg transition-colors duration-200">
            <div className="flex items-center space-x-3">
              <Shield className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium text-gray-900">Privacy Settings</p>
                <p className="text-sm text-gray-600">Manage your data and privacy</p>
              </div>
            </div>
            <span className="text-gray-400">→</span>
          </button>

          <button className="w-full flex items-center justify-between p-3 text-left hover:bg-red-50 rounded-lg transition-colors duration-200 text-red-600">
            <div className="flex items-center space-x-3">
              <X className="w-5 h-5" />
              <div>
                <p className="font-medium">Delete Account</p>
                <p className="text-sm text-red-500">Permanently delete your account and data</p>
              </div>
            </div>
            <span className="text-red-400">→</span>
          </button>
        </div>
      </div>
    </div>
  );
};
