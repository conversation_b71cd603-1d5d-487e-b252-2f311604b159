'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { faceAPI, recommendationAPI, authAPI } from '@/lib/api';
import { FaceAnalysis, ColorRecommendation } from '@/types';
import { PageLayout } from '@/components/layout/PageLayout';
import { WorkflowGuide } from '@/components/workflow/WorkflowGuide';
import {
  Upload,
  History,
  User,
  Camera,
  Palette,
  Eye,
  Sparkles,
  ArrowRight,
  BarChart3,
  Activity
} from 'lucide-react';
import { toast } from 'react-hot-toast';

export default function Dashboard() {
  const { user } = useAuth();
  const [recentAnalyses, setRecentAnalyses] = useState<FaceAnalysis[]>([]);
  const [latestRecommendation, setLatestRecommendation] = useState<ColorRecommendation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [profileData, setProfileData] = useState<any>(null);
  const [stats, setStats] = useState({
    totalAnalyses: 0,
    totalRecommendations: 0,
    lastAnalysisDate: null as string | null
  });

  useEffect(() => {
    console.log('Dashboard mounted, user:', user);
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load recent analyses
      const analysesResponse = await faceAPI.getHistory(1, 5);
      if (analysesResponse.success && analysesResponse.data?.analyses) {
        const analyses = analysesResponse.data.analyses;
        setRecentAnalyses(analyses);
        setStats(prev => ({
          ...prev,
          totalAnalyses: analyses.length,
          lastAnalysisDate: analyses[0]?.createdAt || null
        }));
      }

      // Load latest recommendation
      const recommendationResponse = await recommendationAPI.getLatestRecommendation();
      if (recommendationResponse.success && recommendationResponse.data) {
        setLatestRecommendation(recommendationResponse.data);
      }

      // Test Profile API Call
      console.log('🔄 Testing Profile API call...');
      const profileResponse = await authAPI.getProfile();
      console.log('✅ Profile API Response:', profileResponse);
      if (profileResponse.success && profileResponse.data) {
        // Handle nested response structure: response.data.data.user
        const responseData = profileResponse.data as any;
        let userData = null;

        if (responseData.data && responseData.data.user) {
          userData = responseData.data.user;
          console.log('✅ Extracted user data from nested structure:', userData);
        } else if (responseData.user) {
          userData = responseData.user;
          console.log('✅ Extracted user data from direct structure:', userData);
        } else if (responseData.id) {
          userData = responseData;
          console.log('✅ Using response.data directly:', userData);
        }

        if (userData) {
          setProfileData(userData);
          console.log('✅ Profile data loaded:', userData);
          toast.success(`Profile loaded: ${userData.name}`);
        } else {
          console.error('❌ Could not extract user data from response:', responseData);
          toast.error('Failed to extract profile data');
        }
      } else {
        console.error('❌ Profile API failed:', profileResponse);
        toast.error('Failed to load profile data');
      }

    } catch (error: any) {
      console.error('Failed to load dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Loading Dashboard</h3>
            <p className="text-purple-200">Preparing your style insights...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title={`Welcome back, ${user?.name}!`}
      description="Your personal AI style dashboard - discover, analyze, and perfect your look"
    >
      <div className="max-w-7xl mx-auto px-6 pb-12">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <Link
            href="/dashboard/new-analysis"
            className="group relative overflow-hidden bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-fit mb-4">
                <Upload className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">New Analysis</h3>
              <p className="text-purple-200 text-sm mb-4">Upload a photo to discover your perfect colors</p>
              <div className="flex items-center text-purple-300 text-sm group-hover:text-white transition-colors">
                <span>Start analyzing</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>

          <Link
            href="/model-viewer"
            className="group relative overflow-hidden bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl w-fit mb-4">
                <Eye className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">3D Model Viewer</h3>
              <p className="text-purple-200 text-sm mb-4">See colors on 3D models in real-time</p>
              <div className="flex items-center text-purple-300 text-sm group-hover:text-white transition-colors">
                <span>View models</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>

          <Link
            href="/dashboard/history"
            className="group relative overflow-hidden bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl w-fit mb-4">
                <History className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Analysis History</h3>
              <p className="text-purple-200 text-sm mb-4">Review your past color analyses</p>
              <div className="flex items-center text-purple-300 text-sm group-hover:text-white transition-colors">
                <span>View history</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>

          <Link
            href="/dashboard/profile"
            className="group relative overflow-hidden bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-red-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl w-fit mb-4">
                <User className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-bold text-white mb-2">Profile Settings</h3>
              <p className="text-purple-200 text-sm mb-4">Manage your account and preferences</p>
              <div className="flex items-center text-purple-300 text-sm group-hover:text-white transition-colors">
                <span>Edit profile</span>
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </div>
            </div>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">{stats.totalAnalyses}</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Total Analyses</h3>
            <p className="text-purple-200 text-sm">Face scans completed</p>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">{latestRecommendation?.outfits?.length || 0}</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Color Outfits</h3>
            <p className="text-purple-200 text-sm">Personalized combinations</p>
          </div>

          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl">
                <Activity className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">
                {latestRecommendation ? Math.round(latestRecommendation.confidence * 100) : 0}%
              </span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">AI Confidence</h3>
            <p className="text-purple-200 text-sm">Latest analysis accuracy</p>
          </div>
        </div>

        {/* Profile API Debug Section */}
        <div className="bg-yellow-500/10 backdrop-blur-xl rounded-3xl p-6 border border-yellow-500/20 mb-12">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl">
              <User className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white">🔍 Profile API Test Results</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="text-sm">
                <span className="text-yellow-200 font-medium">API Endpoint:</span>
                <span className="text-white ml-2 font-mono text-xs">https://faceapp-ttwh.onrender.com/api/auth/me</span>
              </div>
              <div className="text-sm">
                <span className="text-yellow-200 font-medium">Status:</span>
                <span className={`ml-2 font-medium ${profileData ? 'text-green-400' : 'text-red-400'}`}>
                  {profileData ? '✅ Success' : '❌ Failed'}
                </span>
              </div>
              <div className="text-sm">
                <span className="text-yellow-200 font-medium">Bearer Token:</span>
                <span className="text-green-400 ml-2 font-medium">✅ Automatically Added</span>
              </div>
            </div>

            {profileData && (
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">User ID:</span>
                  <span className="text-white ml-2 font-mono text-xs">{profileData.id}</span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Name:</span>
                  <span className="text-white ml-2">{profileData.name}</span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Email:</span>
                  <span className="text-white ml-2">{profileData.email}</span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Gender:</span>
                  <span className="text-white ml-2 capitalize">{profileData.gender}</span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Email Verified:</span>
                  <span className={`ml-2 font-medium ${profileData.isEmailVerified ? 'text-green-400' : 'text-red-400'}`}>
                    {profileData.isEmailVerified ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Last Login:</span>
                  <span className="text-white ml-2 text-xs">{profileData.lastLogin ? formatDate(profileData.lastLogin) : 'N/A'}</span>
                </div>
                <div className="text-sm">
                  <span className="text-yellow-200 font-medium">Created:</span>
                  <span className="text-white ml-2 text-xs">{profileData.createdAt ? formatDate(profileData.createdAt) : 'N/A'}</span>
                </div>
              </div>
            )}
          </div>

          <div className="mt-6 pt-4 border-t border-yellow-500/20">
            <button
              onClick={loadDashboardData}
              className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl hover:shadow-lg transition-all duration-300 text-sm font-medium"
            >
              🔄 Refresh Profile Data
            </button>
          </div>
        </div>

        {/* Workflow Guide */}
        <div className="mb-12">
          <WorkflowGuide
            currentStep={4}
            completedSteps={[1, 2, 3, 4]}
          />
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Analyses */}
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white">Recent Analyses</h3>
              <Link
                href="/dashboard/history"
                className="text-purple-300 hover:text-white text-sm flex items-center gap-2 transition-colors"
              >
                View all
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
            
            {recentAnalyses.length > 0 ? (
              <div className="space-y-4">
                {recentAnalyses.slice(0, 3).map((analysis, index) => (
                  <div key={analysis._id} className="flex items-center gap-4 p-4 bg-white/5 rounded-2xl">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                      <Camera className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium">Analysis #{index + 1}</p>
                      <p className="text-purple-200 text-sm">{formatDate(analysis.createdAt)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-emerald-300 text-sm font-medium">Completed</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Camera className="w-12 h-12 text-purple-300 mx-auto mb-3" />
                <p className="text-purple-200">No analyses yet</p>
                <p className="text-purple-300 text-sm">Upload your first photo to get started</p>
              </div>
            )}
          </div>

          {/* Latest Recommendation */}
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white">Latest Colors</h3>
              <Link
                href="/model-viewer"
                className="text-purple-300 hover:text-white text-sm flex items-center gap-2 transition-colors"
              >
                Try on 3D
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
            
            {latestRecommendation?.outfits ? (
              <div className="space-y-4">
                {latestRecommendation.outfits.slice(0, 3).map((outfit, index) => (
                  <div key={index} className="p-4 bg-white/5 rounded-2xl">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{outfit.outfitName}</h4>
                      <div className="flex gap-2">
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.shirt.hex }}
                        />
                        <div
                          className="w-6 h-6 rounded-full border border-white/30"
                          style={{ backgroundColor: outfit.pants.hex }}
                        />
                        {outfit.shoes && (
                          <div
                            className="w-6 h-6 rounded-full border border-white/30"
                            style={{ backgroundColor: outfit.shoes.hex }}
                          />
                        )}
                      </div>
                    </div>
                    <p className="text-purple-200 text-sm">{outfit.overallReason}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Palette className="w-12 h-12 text-purple-300 mx-auto mb-3" />
                <p className="text-purple-200">No color recommendations yet</p>
                <p className="text-purple-300 text-sm">Complete an analysis to see your colors</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
