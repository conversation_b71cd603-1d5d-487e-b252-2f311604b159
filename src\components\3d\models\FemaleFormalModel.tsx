'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import { Color } from 'three';
import * as THREE from 'three';

interface FemaleFormalModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const FemaleFormalModel: React.FC<FemaleFormalModelProps> = ({ 
  colorCombination, 
  enableColorSync = false 
}) => {
  const group = useRef<THREE.Group>(null);
  const { scene: formalGltf } = useGLTF('/differentmessformal.glb') as any;

  // Color mapping for formal wear
  const formalColorMap = {
    skin: new Color(0xfdbcb4), // Natural skin tone
    hair: new Color(0x4a2c17), // Dark brown hair
    dress: colorCombination ? new Color(colorCombination.shirt) : new Color(0x1e3a8a), // Navy dress default
    accessories: colorCombination ? new Color(colorCombination.pants) : new Color(0x374151), // Dark accessories
    shoes: colorCombination ? new Color(colorCombination.shoes) : new Color(0x000000), // Black shoes default
    jewelry: new Color(0xffd700), // Gold jewelry
    clothing: new Color(0x6b7280) // Default gray clothing
  };

  // Apply colors to formal model
  useEffect(() => {
    if (!formalGltf) return;

    console.log('🔄 Updating female formal model colors...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    let updatedMeshes = 0;

    // Update color map if color combination is provided
    if (colorCombination && enableColorSync) {
      formalColorMap.dress = new Color(colorCombination.shirt);
      formalColorMap.accessories = new Color(colorCombination.pants);
      formalColorMap.shoes = new Color(colorCombination.shoes);
    }

    formalGltf.traverse((child: any) => {
      if (child.isMesh && child.name) {
        const meshName = child.name.toLowerCase();
        let targetColor = formalColorMap.clothing; // default

        // Determine color based on mesh name for formal wear
        if (meshName.includes('face') || meshName.includes('hand') || 
            meshName.includes('arm') || meshName.includes('leg') ||
            meshName.includes('body') || meshName.includes('skin')) {
          targetColor = formalColorMap.skin;
        } else if (meshName.includes('hair')) {
          targetColor = formalColorMap.hair;
        } else if (meshName.includes('dress') || meshName.includes('gown') ||
                   meshName.includes('top') || meshName.includes('blouse') ||
                   meshName.includes('shirt') || meshName.includes('jacket')) {
          targetColor = formalColorMap.dress;
        } else if (meshName.includes('skirt') || meshName.includes('bottom') ||
                   meshName.includes('pants') || meshName.includes('trouser')) {
          targetColor = formalColorMap.accessories;
        } else if (meshName.includes('shoe') || meshName.includes('heel') ||
                   meshName.includes('boot') || meshName.includes('sandal')) {
          targetColor = formalColorMap.shoes;
        } else if (meshName.includes('jewelry') || meshName.includes('necklace') ||
                   meshName.includes('earring') || meshName.includes('bracelet') ||
                   meshName.includes('ring') || meshName.includes('watch')) {
          targetColor = formalColorMap.jewelry;
        } else if (meshName.includes('belt') || meshName.includes('bag') ||
                   meshName.includes('purse') || meshName.includes('clutch')) {
          targetColor = formalColorMap.accessories;
        }

        // Apply color to mesh material
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat: any) => {
              if (mat.color) {
                mat.color.copy(targetColor);
                // Ensure material properties are preserved
                mat.transparent = mat.transparent || false;
                mat.opacity = mat.opacity || 1.0;
                mat.visible = true;
                mat.needsUpdate = true;
              }
            });
          } else if (child.material.color) {
            child.material.color.copy(targetColor);
            // Ensure material properties are preserved
            child.material.transparent = child.material.transparent || false;
            child.material.opacity = child.material.opacity || 1.0;
            child.material.visible = true;
            child.material.needsUpdate = true;
          }
          updatedMeshes++;
        }
      }
    });

    console.log(`✅ Updated ${updatedMeshes} meshes on female formal model`);
  }, [colorCombination, enableColorSync, formalGltf]);

  if (!formalGltf) {
    console.log('⏳ Female formal model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[1, 1, 1]} position={[0, -1, 0]}>
      <primitive object={formalGltf} />
    </group>
  );
};

// Preload the model
useGLTF.preload('/differentmessformal.glb');
