'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import { Color } from 'three';
import * as THREE from 'three';

interface FemaleCheongsameProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const FemaleCheongsam: React.FC<FemaleCheongsameProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { scene: cheongsameGltf } = useGLTF('/onepice.glb') as any;

  const colorMap = {
    skin: new Color(0xfdbcb4),
    hair: new Color(0x000000),
    dress: colorCombination ? new Color(colorCombination.shirt) : new Color(0xdc2626),
    trim: colorCombination ? new Color(colorCombination.pants) : new Color(0xffd700),
    shoes: colorCombination ? new Color(colorCombination.shoes) : new Color(0x000000),
    accessories: new Color(0xffd700),
    clothing: new Color(0x6b7280)
  };

  useEffect(() => {
    if (!cheongsameGltf) return;

    let updatedMeshes = 0;

    if (colorCombination && enableColorSync) {
      colorMap.dress = new Color(colorCombination.shirt);
      colorMap.trim = new Color(colorCombination.pants);
      colorMap.shoes = new Color(colorCombination.shoes);
    }

    cheongsameGltf.traverse((child: any) => {
      if (child.isMesh && child.name) {
        const meshName = child.name.toLowerCase();
        let targetColor = colorMap.clothing;

        if (meshName.includes('face') || meshName.includes('hand') || 
            meshName.includes('arm') || meshName.includes('leg') ||
            meshName.includes('body') || meshName.includes('skin')) {
          targetColor = colorMap.skin;
        } else if (meshName.includes('hair')) {
          targetColor = colorMap.hair;
        } else if (meshName.includes('cheongsam') || meshName.includes('dress') ||
                   meshName.includes('qipao') || meshName.includes('main')) {
          targetColor = colorMap.dress;
        } else if (meshName.includes('trim') || meshName.includes('border') ||
                   meshName.includes('collar') || meshName.includes('button')) {
          targetColor = colorMap.trim;
        } else if (meshName.includes('shoe') || meshName.includes('heel')) {
          targetColor = colorMap.shoes;
        }

        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat: any) => {
              if (mat.color) {
                mat.color.copy(targetColor);
                // Ensure material properties are preserved
                mat.transparent = mat.transparent || false;
                mat.opacity = mat.opacity || 1.0;
                mat.visible = true;
                mat.needsUpdate = true;
              }
            });
          } else if (child.material.color) {
            child.material.color.copy(targetColor);
            // Ensure material properties are preserved
            child.material.transparent = child.material.transparent || false;
            child.material.opacity = child.material.opacity || 1.0;
            child.material.visible = true;
            child.material.needsUpdate = true;
          }
          updatedMeshes++;
        }
      }
    });
  }, [colorCombination, enableColorSync, cheongsameGltf]);

  if (!cheongsameGltf) {
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[1, 1, 1]} position={[0, -1, 0]}>
      <primitive object={cheongsameGltf} />
    </group>
  );
};

useGLTF.preload('/onepice.glb');
