'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import {
  Home,
  Upload,
  History,
  User,
  Settings,
  LogOut,
  Menu,
  X,
  Sparkles,
  Eye,
  Palette,
  Camera,
  Star
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { MobileTabNavigation } from './MobileTabNavigation';

export const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      router.push('/auth');
    } catch (error) {
      toast.error('Failed to logout');
    }
  };

  const navItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'New Analysis',
      href: '/dashboard/new-analysis',
      icon: Upload,
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      name: 'History',
      href: '/dashboard/history',
      icon: History,
      gradient: 'from-emerald-500 to-teal-500'
    },
    {
      name: '3D Model Viewer',
      href: '/model-viewer',
      icon: Eye,
      gradient: 'from-orange-500 to-red-500'
    },
    {
      name: 'Profile',
      href: '/dashboard/profile',
      icon: User,
      gradient: 'from-indigo-500 to-purple-500'
    }
  ];

  const isActive = (href: string) => pathname === href;

  return (
    <>
      {/* Desktop Navbar */}
      <nav className="hidden lg:flex fixed top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-xl border-b border-white/20">
        <div className="max-w-7xl mx-auto px-6 py-4 w-full">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/dashboard" className="flex items-center gap-3 group">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                  Face App
                </h1>
                <p className="text-xs text-purple-200">AI Style Studio</p>
              </div>
            </Link>

            {/* Navigation Items */}
            <div className="flex items-center gap-2">
              {navItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`group relative px-4 py-2 rounded-xl transition-all duration-300 ${
                      isActive(item.href)
                        ? `bg-gradient-to-r ${item.gradient} text-white shadow-lg`
                        : 'text-white/80 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <IconComponent className="w-4 h-4" />
                      <span className="text-sm font-medium">{item.name}</span>
                    </div>
                    {isActive(item.href) && (
                      <div className="absolute inset-0 bg-white/20 rounded-xl"></div>
                    )}
                  </Link>
                );
              })}
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm font-medium text-white">{user?.name}</p>
                <p className="text-xs text-purple-200">{user?.email}</p>
              </div>
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-white/80 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-300"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Top Bar - Simplified */}
      <nav className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-xl border-b border-white/10">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-bold text-white">Face App</span>
            </Link>

            {/* User Info */}
            <div className="flex items-center gap-3">
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-white">{user?.name}</p>
                <p className="text-xs text-purple-200">{user?.email}</p>
              </div>
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Bottom Tab Navigation */}
      <MobileTabNavigation />

      {/* Spacer for fixed navbar */}
      <div className="h-20 lg:h-24"></div>
    </>
  );
};
