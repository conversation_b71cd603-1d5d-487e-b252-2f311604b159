'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import { Color } from 'three';
import * as THREE from 'three';

interface FemaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const FemaleModel: React.FC<FemaleModelProps> = ({ 
  colorCombination, 
  enableColorSync = false 
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/female.glb') as any;

  // Intelligent color assignment for female model
  const getIntelligentColor = (materialName: string, index: number, meshName: string = ''): Color => {
    const safeMaterialName = materialName || '';
    const safeMeshName = meshName || '';
    const name = safeMaterialName.toLowerCase();
    const mesh = safeMeshName.toLowerCase();

    console.log(`🎨 Female model - Assigning color for material: "${safeMaterialName}" on mesh: "${safeMeshName}"`);

    // Force skin tone for Node meshes
    if (mesh.toLowerCase().includes('node') || name.toLowerCase().includes('node')) {
      console.log('   → FORCED skin tone for Node mesh:', safeMeshName, safeMaterialName);
      return new Color(0xfdbcb4); // Natural skin tone
    }

    // Use dress colors if available and color sync is enabled
    if (enableColorSync && colorCombination) {
      // FIRST: Apply pants color to leg dress/pants meshes (check this BEFORE dress)
      if (mesh.includes('legdress') || mesh.includes('pants') ||
          mesh.includes('legdress.001') || mesh.includes('legdress.002') || mesh.includes('legdress.003') ||
          name.includes('legdress') || name.includes('pants') ||
          name.includes('legdress.001') || name.includes('legdress.002') || name.includes('legdress.003')) {
        console.log('   → Applied PANTS color to legdress meshes:', colorCombination.pants);

        // Always make pants different from shirt
        const shirtColor = new Color(colorCombination.shirt);
        const pantsColor = new Color(colorCombination.pants);

        if (shirtColor.getHexString() === pantsColor.getHexString()) {
          // Colors are the same, make pants much darker
          const darkerPants = pantsColor.clone().multiplyScalar(0.5); // 50% darker
          console.log('   → Making pants darker than shirt:', darkerPants.getHexString());
          return darkerPants;
        } else {
          // Colors are different, use original pants color
          console.log('   → Using original pants color:', pantsColor.getHexString());
          return pantsColor;
        }
      }

      // SECOND: Apply shirt color to dress meshes AND hand meshes (but NOT legdress)
      if ((mesh.includes('dress') && !mesh.includes('legdress')) ||
          (mesh.includes('backdress') && !mesh.includes('legdress')) ||
          (name.includes('dress') && !name.includes('legdress')) ||
          (name.includes('backdress') && !name.includes('legdress')) ||
          mesh.includes('hand.003') || mesh.includes('hand.002') ||
          mesh.includes('hand.001') || mesh.includes('hand') ||
          name.includes('hand.003') || name.includes('hand.002') ||
          name.includes('hand.001') || name.includes('hand')) {
        console.log('   → Applied SHIRT color to dress/hands:', colorCombination.shirt);
        return new Color(colorCombination.shirt);
      }

      // Apply shoes color to shoes/heels meshes
      if (mesh.includes('hells') || mesh.includes('shoes') ||
          name.includes('hells') || name.includes('shoes')) {
        console.log('   → Applied shoes color:', colorCombination.shoes);
        return new Color(colorCombination.shoes);
      }
    }

    // Default colors for body parts (not affected by color combinations)
    // Check both mesh name and material name for skin parts (case-insensitive)
    if (mesh.includes('face') || mesh.includes('ear') || mesh.includes('hand') ||
        mesh.includes('plam') || mesh.includes('leg') || mesh.includes('node1024') ||
        mesh.includes('node1026') || mesh.includes('body') ||
        mesh.toLowerCase().includes('node1.006') || mesh.toLowerCase().includes('node0.006') ||
        name.toLowerCase().includes('node1.006') || name.toLowerCase().includes('node0.006') ||
        name.includes('face') || name.includes('ear') || name.includes('hand') ||
        name.includes('body') || name.includes('skin')) {
      console.log('   → Applied skin tone for body mesh:', meshName);
      return new Color(0xfdbcb4); // Natural skin tone
    }
    if (mesh.includes('hair')) {
      console.log('   → Applied black color for hair mesh');
      return new Color('#000000'); // Force black hair
    }
    if (mesh.includes('eye')) {
      console.log('   → Applied dark color for eyes mesh');
      return new Color('#000000'); // Black eyes
    }

    // Default dress colors when no color combination is available
    if (mesh.includes('dress') || name.includes('dress')) {
      console.log('   → Applied default dress color');
      return new Color(0xe11d48); // Default red dress
    }
    if (mesh.includes('hells') || mesh.includes('shoes') || name.includes('hells') || name.includes('shoes')) {
      console.log('   → Applied default shoes color');
      return new Color(0x1f2937); // Default dark shoes
    }

    // Generate consistent color based on material name hash
    let hash = 0;
    const hashString = safeMaterialName || `material_${index}`;
    for (let i = 0; i < hashString.length; i++) {
      hash = ((hash << 5) - hash + hashString.charCodeAt(i)) & 0xffffffff;
    }

    // Generate pleasant colors (avoid pure black/white)
    const hue = Math.abs(hash) % 360;
    const saturation = 40 + (Math.abs(hash >> 8) % 40); // 40-80%
    const lightness = 30 + (Math.abs(hash >> 16) % 40); // 30-70%

    return new Color().setHSL(hue / 360, saturation / 100, lightness / 100);
  };

  // Apply colors to materials when color combination changes
  useEffect(() => {
    if (!materials) return;

    console.log('🔄 Updating female model colors...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    let updatedMaterials = 0;

    // Apply colors to all materials
    Object.entries(materials).forEach(([materialName, material], index) => {
      if (material && typeof material === 'object' && 'color' in material) {
        const mat = material as THREE.MeshStandardMaterial;
        const newColor = getIntelligentColor(materialName, index, '');

        if (mat.color && !mat.color.equals(newColor)) {
          mat.color.copy(newColor);
          // Ensure material properties are preserved
          mat.transparent = mat.transparent || false;
          mat.opacity = mat.opacity || 1.0;
          mat.visible = true;
          mat.needsUpdate = true;
          updatedMaterials++;
        }
      }
    });

    console.log(`✅ Updated ${updatedMaterials} materials on female model`);
  }, [colorCombination, enableColorSync, materials]);

  if (!nodes || !materials) {
    console.log('⏳ Female model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[1, 1, 1]} position={[0, -1, 0]}>
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1?.geometry}
        material={materials.x1}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1001?.geometry}
        material={materials['x1.001']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1002?.geometry}
        material={materials['x1.002']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1003?.geometry}
        material={materials['x1.003']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1004?.geometry}
        material={materials['x1.004']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.rightleg?.geometry}
        material={materials['x1.005']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.righthandlelittlecloth?.geometry}
        material={materials['x1.006']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.righthandpam?.geometry}
        material={materials['x1.007']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.righthand?.geometry}
        material={materials['x1.008']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.righteye?.geometry}
        material={materials['x1.009']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.leftleg?.geometry}
        material={materials['x1.010']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.lefthandlittlecloth?.geometry}
        material={materials['x1.011']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.lefteye?.geometry}
        material={materials['x1.012']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.rightlegheel?.geometry}
        material={materials['x1.013']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.leftlegheel?.geometry}
        material={materials['x1.014']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Node1015?.geometry}
        material={materials['x1.015']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.hair?.geometry}
        material={materials['x1.016']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.lefthand?.geometry}
        material={materials['x1.017']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.frontdress?.geometry}
        material={materials['x1.018']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.lefthandpam?.geometry}
        material={materials['x1.019']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.face?.geometry}
        material={materials['x1.020']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.backcloth?.geometry}
        material={materials['x1.021']}
        position={[0.22, -1.758, 0.074]}
        rotation={[1.672, 0, 0]}
      />
    </group>
  );
};

// Preload the model
useGLTF.preload('/female.glb');
