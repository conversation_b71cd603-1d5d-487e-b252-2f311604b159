'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface ColorCombination {
  id: string;
  shirt: string;
  pants: string;
  shoes: string;
  description: string;
  name: string;
}

interface MaleModelProps {
  // No props needed - component is self-contained
}

export const MaleModel: React.FC<MaleModelProps> = () => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;



  // State for color recommendations and selection
  const [colorCombinations, setColorCombinations] = useState<ColorCombination[]>([]);
  const [selectedCombination, setSelectedCombination] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  // Refs for direct material access
  const sweaterRef = useRef<THREE.SkinnedMesh>(null);
  const collarRef = useRef<THREE.SkinnedMesh>(null);
  const pantsRef = useRef<THREE.SkinnedMesh>(null);
  const shoesRef = useRef<THREE.SkinnedMesh>(null);

  // Force re-render when color combination changes
  const [forceUpdate, setForceUpdate] = useState(0);

  // Load color recommendations when component mounts
  useEffect(() => {
    loadColorRecommendations();
  }, []);

  const loadColorRecommendations = async () => {
    setIsLoading(true);
    try {
      console.log('🎨 MaleModel: Loading latest color recommendations...');

      // Import and use the recommendationAPI
      const { recommendationAPI } = await import('../../../lib/api');
      const response = await recommendationAPI.getLatestRecommendation();

      if (response.success && response.data) {
        console.log('✅ MaleModel: Got API response:', response.data);

        let outfits = [];

        // Handle different response structures - use type assertion for flexibility
        const responseData = response.data as any;
        if (responseData.recommendations) {
          outfits = responseData.recommendations;
        } else if (responseData.outfits) {
          outfits = responseData.outfits;
        } else if (responseData.data?.recommendations) {
          outfits = responseData.data.recommendations;
        }

        if (outfits && outfits.length > 0) {
          console.log('✅ MaleModel: Found outfits:', outfits.length);

          // Convert to color combinations format
          const combinations = outfits.map((outfit: any, index: number) => ({
            id: `latest-${index}`,
            shirt: outfit.shirt?.hex || '#3498db',
            pants: outfit.pants?.hex || '#2c3e50',
            shoes: outfit.shoes?.hex || '#000000',
            description: outfit.outfitName || `AI Style ${index + 1}`,
            name: outfit.outfitName || `Style ${index + 1}`
          }));

          setColorCombinations(combinations);
          console.log('🎨 MaleModel: Set color combinations:', combinations);
        } else {
          console.log('⚠️ MaleModel: No outfits found in response');
          setColorCombinations([]);
        }
      } else {
        console.log('⚠️ MaleModel: API response failed or no data');
        setColorCombinations([]);
      }
    } catch (error) {
      console.error('❌ MaleModel: Error loading recommendations:', error);
      setColorCombinations([]);
    } finally {
      setIsLoading(false);
    }
  };

  if (!nodes || !materials) {
    console.log('⏳ Male model not loaded yet...');
    return null;
  }

  // Apply colors when selected combination changes
  useEffect(() => {
    if (colorCombinations.length === 0 || selectedCombination < 0 || selectedCombination >= colorCombinations.length) return;

    const currentCombination = colorCombinations[selectedCombination];
    console.log('🎨 Applying colors directly to mesh materials via refs...');
    console.log('Selected combination:', currentCombination);

    // Apply colors to each mesh directly
    if (sweaterRef.current?.material) {
      const sweaterMat = sweaterRef.current.material as THREE.MeshStandardMaterial;
      if (sweaterMat.color) {
        sweaterMat.color.set(currentCombination.shirt);
        sweaterMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${currentCombination.shirt} to sweater`);
      }
    }

    if (collarRef.current?.material) {
      const collarMat = collarRef.current.material as THREE.MeshStandardMaterial;
      if (collarMat.color) {
        collarMat.color.set(currentCombination.shirt);
        collarMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${currentCombination.shirt} to collar`);
      }
    }

    if (pantsRef.current?.material) {
      const pantsMat = pantsRef.current.material as THREE.MeshStandardMaterial;
      if (pantsMat.color) {
        pantsMat.color.set(currentCombination.pants);
        pantsMat.needsUpdate = true;
        console.log(`✅ Applied pants color ${currentCombination.pants} to pants`);
      }
    }

    if (shoesRef.current?.material) {
      const shoesMat = shoesRef.current.material as THREE.MeshStandardMaterial;
      if (shoesMat.color) {
        shoesMat.color.set(currentCombination.shoes);
        shoesMat.needsUpdate = true;
        console.log(`✅ Applied shoes color ${currentCombination.shoes} to shoes`);
      }
    }

    // Force re-render
    setForceUpdate(prev => prev + 1);
  }, [colorCombinations, selectedCombination]);

  // Function to handle color combination selection (can be called from UI)
  const handleColorCombinationSelect = (index: number) => {
    if (index >= 0 && index < colorCombinations.length) {
      setSelectedCombination(index);
      console.log('🎨 MaleModel: Selected combination index:', index);
    }
  };

  return (
    <>
      {/* 3D Model */}
      <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]} key={`male-model-${forceUpdate}`}>
        <group name="Scene">
          <group name="Armature">
            {/* Body - Keep original skin color */}
            <skinnedMesh
              name="Ch31_Body"
              geometry={nodes.Ch31_Body?.geometry}
              material={materials.Ch31_body}
              skeleton={nodes.Ch31_Body?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Collar - Apply shirt color */}
            <skinnedMesh
              ref={collarRef}
              name="Ch31_Collar"
              geometry={nodes.Ch31_Collar?.geometry}
              material={materials.Ch31_body?.clone()}
              skeleton={nodes.Ch31_Collar?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Eyelashes - Keep hair color */}
            <skinnedMesh
              name="Ch31_Eyelashes"
              geometry={nodes.Ch31_Eyelashes?.geometry}
              material={materials.Ch31_hair}
              skeleton={nodes.Ch31_Eyelashes?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Hair - Keep original hair color */}
            <skinnedMesh
              name="Ch31_Hair"
              geometry={nodes.Ch31_Hair?.geometry}
              material={materials.Ch31_hair}
              skeleton={nodes.Ch31_Hair?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Pants - Apply pants color */}
            <skinnedMesh
              ref={pantsRef}
              name="Ch31_Pants"
              geometry={nodes.Ch31_Pants?.geometry}
              material={materials.Ch31_body?.clone()}
              skeleton={nodes.Ch31_Pants?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Shoes - Apply shoes color */}
            <skinnedMesh
              ref={shoesRef}
              name="Ch31_Shoes"
              geometry={nodes.Ch31_Shoes?.geometry}
              material={materials.Ch31_body?.clone()}
              skeleton={nodes.Ch31_Shoes?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Sweater - Apply shirt color */}
            <skinnedMesh
              ref={sweaterRef}
              name="Ch31_Sweater"
              geometry={nodes.Ch31_Sweater?.geometry}
              material={materials.Ch31_body?.clone()}
              skeleton={nodes.Ch31_Sweater?.skeleton}
              castShadow
              receiveShadow
            />

            {/* Armature */}
            <primitive object={nodes.mixamorig9Hips} />
          </group>
        </group>
      </group>

      {/* Color Combinations UI - positioned in 3D space */}
      {colorCombinations.length > 0 && (
        <group position={[2, 1, 0]}>
          {colorCombinations.map((combination, index) => (
            <mesh
              key={combination.id}
              position={[0, -index * 0.3, 0]}
              onClick={() => handleColorCombinationSelect(index)}
            >
              <boxGeometry args={[0.2, 0.2, 0.05]} />
              <meshStandardMaterial
                color={selectedCombination === index ? '#ffffff' : '#cccccc'}
                transparent
                opacity={selectedCombination === index ? 1.0 : 0.7}
              />
            </mesh>
          ))}
        </group>
      )}
    </>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');
