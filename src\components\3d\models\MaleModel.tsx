'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;

  // Refs to track actual mesh instances
  const sweaterMeshRef = useRef<THREE.SkinnedMesh>(null);
  const pantsMeshRef = useRef<THREE.SkinnedMesh>(null);
  const collarMeshRef = useRef<THREE.SkinnedMesh>(null);
  const shoesMeshRef = useRef<THREE.SkinnedMesh>(null);

  // Debug logging for props and model structure
  useEffect(() => {
    console.log('🔄 MaleModel: Props changed');
    console.log('  - colorCombination:', colorCombination);
    console.log('  - enableColorSync:', enableColorSync);
    console.log('  - materials available:', !!materials);
    console.log('  - nodes available:', !!nodes);

    if (materials) {
      console.log('📋 Available materials:', Object.keys(materials));
    }
    if (nodes) {
      console.log('📋 Available nodes:', Object.keys(nodes));

      // Check which material each mesh is supposed to use
      if (nodes.Ch31_Sweater) {
        console.log('👕 Ch31_Sweater mesh found');
      }
      if (nodes.Ch31_Pants) {
        console.log('👖 Ch31_Pants mesh found');
      }
      if (nodes.Ch31_Collar) {
        console.log('👔 Ch31_Collar mesh found');
      }
      if (nodes.Ch31_Shoes) {
        console.log('👞 Ch31_Shoes mesh found');
      }
    }
  }, [colorCombination, enableColorSync, materials, nodes]);

  // Create new materials whenever color combination changes
  const finalMaterials = useMemo(() => {
    if (!colorCombination || !enableColorSync || !materials?.Ch31_body) {
      return {
        sweater: materials?.Ch31_body,
        collar: materials?.Ch31_body,
        pants: materials?.Ch31_body,
        shoes: materials?.Ch31_body,
      };
    }

    console.log('🎨 Creating new colored materials for:', colorCombination);

    // Create completely new materials with colors applied
    const sweaterMaterial = materials.Ch31_body.clone();
    sweaterMaterial.name = 'sweater_material';
    sweaterMaterial.color.set(colorCombination.shirt);
    sweaterMaterial.transparent = false;
    sweaterMaterial.opacity = 1.0;
    sweaterMaterial.needsUpdate = true;

    const collarMaterial = materials.Ch31_body.clone();
    collarMaterial.name = 'collar_material';
    collarMaterial.color.set(colorCombination.shirt);
    collarMaterial.transparent = false;
    collarMaterial.opacity = 1.0;
    collarMaterial.needsUpdate = true;

    const pantsMaterial = materials.Ch31_body.clone();
    pantsMaterial.name = 'pants_material';
    pantsMaterial.color.set(colorCombination.pants);
    pantsMaterial.transparent = false;
    pantsMaterial.opacity = 1.0;
    pantsMaterial.needsUpdate = true;

    const shoesMaterial = materials.Ch31_body.clone();
    shoesMaterial.name = 'shoes_material';
    shoesMaterial.color.set(colorCombination.shoes);
    shoesMaterial.transparent = false;
    shoesMaterial.opacity = 1.0;
    shoesMaterial.needsUpdate = true;

    console.log(`✅ Created new materials with colors:`);
    console.log(`  - Sweater: ${colorCombination.shirt} (material:`, sweaterMaterial, ')');
    console.log(`  - Collar: ${colorCombination.shirt} (material:`, collarMaterial, ')');
    console.log(`  - Pants: ${colorCombination.pants} (material:`, pantsMaterial, ')');
    console.log(`  - Shoes: ${colorCombination.shoes} (material:`, shoesMaterial, ')');

    return {
      sweater: sweaterMaterial,
      collar: collarMaterial,
      pants: pantsMaterial,
      shoes: shoesMaterial,
    };
  }, [colorCombination, enableColorSync, materials]);

  // Force material updates after render
  useEffect(() => {
    if (!finalMaterials || !colorCombination || !enableColorSync) return;

    // Force all materials to update their rendering
    Object.values(finalMaterials).forEach(material => {
      if (material && material.needsUpdate !== undefined) {
        material.needsUpdate = true;
      }
    });

    console.log('🔄 Forced material updates for visual refresh');
  }, [finalMaterials, colorCombination, enableColorSync]);

  // Force material assignment after render
  useEffect(() => {
    if (!colorCombination || !enableColorSync || !finalMaterials) return;

    // Directly assign materials to meshes after render
    setTimeout(() => {
      console.log('� Forcing material assignment to meshes...');

      if (sweaterMeshRef.current && finalMaterials.sweater) {
        sweaterMeshRef.current.material = finalMaterials.sweater;
        console.log('  ✅ Assigned sweater material:', finalMaterials.sweater.color.getHexString());
      }

      if (pantsMeshRef.current && finalMaterials.pants) {
        pantsMeshRef.current.material = finalMaterials.pants;
        console.log('  ✅ Assigned pants material:', finalMaterials.pants.color.getHexString());
      }

      if (collarMeshRef.current && finalMaterials.collar) {
        collarMeshRef.current.material = finalMaterials.collar;
        console.log('  ✅ Assigned collar material:', finalMaterials.collar.color.getHexString());
      }

      if (shoesMeshRef.current && finalMaterials.shoes) {
        shoesMeshRef.current.material = finalMaterials.shoes;
        console.log('  ✅ Assigned shoes material:', finalMaterials.shoes.color.getHexString());
      }

      // Check what materials are actually applied after assignment
      setTimeout(() => {
        console.log('🔍 Verifying actual mesh materials after assignment:');

        if (sweaterMeshRef.current) {
          const mat = sweaterMeshRef.current.material as THREE.MeshStandardMaterial;
          console.log('  - Sweater mesh material color:', mat.color.getHexString());
        }

        if (pantsMeshRef.current) {
          const mat = pantsMeshRef.current.material as THREE.MeshStandardMaterial;
          console.log('  - Pants mesh material color:', mat.color.getHexString());
        }

        if (collarMeshRef.current) {
          const mat = collarMeshRef.current.material as THREE.MeshStandardMaterial;
          console.log('  - Collar mesh material color:', mat.color.getHexString());
        }

        if (shoesMeshRef.current) {
          const mat = shoesMeshRef.current.material as THREE.MeshStandardMaterial;
          console.log('  - Shoes mesh material color:', mat.color.getHexString());
        }
      }, 50);
    }, 100); // Small delay to ensure render is complete
  }, [colorCombination, enableColorSync, finalMaterials]);

  if (!nodes || !materials) {
    console.log('⏳ Model not loaded yet...');
    return null;
  }

  // Debug: Check if meshes exist and log material assignments
  console.log('🎭 Rendering model with materials:');
  console.log('  - Ch31_Sweater exists:', !!nodes.Ch31_Sweater, 'material:', finalMaterials.sweater?.color?.getHexString());
  console.log('  - Ch31_Pants exists:', !!nodes.Ch31_Pants, 'material:', finalMaterials.pants?.color?.getHexString());
  console.log('  - Ch31_Collar exists:', !!nodes.Ch31_Collar, 'material:', finalMaterials.collar?.color?.getHexString());
  console.log('  - Ch31_Shoes exists:', !!nodes.Ch31_Shoes, 'material:', finalMaterials.shoes?.color?.getHexString());

  return (
    <group
      ref={group}
      dispose={null}
      scale={[0.01, 0.01, 0.01]}
      position={[0, -1, 0]}
      rotation={[Math.PI / 2, 0, 0]}
      key={`male-model-${colorCombination?.shirt || 'default'}`}
    >
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            ref={collarMeshRef}
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={finalMaterials.collar}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            ref={pantsMeshRef}
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={finalMaterials.pants}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            ref={shoesMeshRef}
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={finalMaterials.shoes}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            ref={sweaterMeshRef}
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={finalMaterials.sweater}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');