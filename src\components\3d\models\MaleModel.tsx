'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;



  // Force re-render when color combination changes
  const [forceUpdate, setForceUpdate] = React.useState(0);

  // Debug logging and force update when color combination changes
  useEffect(() => {
    if (!materials || !nodes) return;

    console.log('🔄 Male model color combination updated...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    // Debug: Log all available materials and nodes
    console.log('📋 Available materials in temp.glb:');
    Object.keys(materials).forEach((materialName, index) => {
      console.log(`  ${index + 1}. "${materialName}"`);
    });

    console.log('📋 Available nodes/meshes in temp.glb:');
    Object.keys(nodes).forEach((nodeName, index) => {
      console.log(`  ${index + 1}. "${nodeName}"`);
    });

    if (colorCombination && enableColorSync) {
      console.log('🎨 Colors will be applied to:');
      console.log(`  - Sweater/Collar: ${colorCombination.shirt}`);
      console.log(`  - Pants: ${colorCombination.pants}`);
      console.log(`  - Shoes: ${colorCombination.shoes}`);

      // Force component re-render to apply new colors
      setForceUpdate(prev => prev + 1);
    }
  }, [colorCombination, enableColorSync, materials, nodes]);

  if (!nodes || !materials) {
    console.log('⏳ Male model not loaded yet...');
    return null;
  }

  // Apply colors directly to materials when color combination changes
  useEffect(() => {
    if (!materials || !colorCombination || !enableColorSync) return;

    console.log('🎨 Applying colors directly to materials...');
    console.log('Available materials:', Object.keys(materials));
    console.log('Color combination:', colorCombination);

    // Apply colors directly to the base materials
    if (materials.Ch31_body) {
      // Clone the base material for each clothing piece
      const sweaterMaterial = materials.Ch31_body.clone();
      const collarMaterial = materials.Ch31_body.clone();
      const pantsMaterial = materials.Ch31_body.clone();
      const shoesMaterial = materials.Ch31_body.clone();

      // Apply shirt color to sweater and collar
      sweaterMaterial.color.set(colorCombination.shirt);
      sweaterMaterial.needsUpdate = true;
      collarMaterial.color.set(colorCombination.shirt);
      collarMaterial.needsUpdate = true;

      // Apply pants color to pants
      pantsMaterial.color.set(colorCombination.pants);
      pantsMaterial.needsUpdate = true;

      // Apply shoes color to shoes
      shoesMaterial.color.set(colorCombination.shoes);
      shoesMaterial.needsUpdate = true;

      // Store the materials for use in render
      materials._sweaterMaterial = sweaterMaterial;
      materials._collarMaterial = collarMaterial;
      materials._pantsMaterial = pantsMaterial;
      materials._shoesMaterial = shoesMaterial;

      console.log('✅ Applied colors to materials:');
      console.log(`  - Sweater: ${colorCombination.shirt}`);
      console.log(`  - Collar: ${colorCombination.shirt}`);
      console.log(`  - Pants: ${colorCombination.pants}`);
      console.log(`  - Shoes: ${colorCombination.shoes}`);

      // Force re-render
      setForceUpdate(prev => prev + 1);
    }
  }, [colorCombination, enableColorSync, materials]);

  return (
    <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]} key={`male-model-${forceUpdate}`}>
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={materials._collarMaterial || materials.Ch31_body}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={materials._pantsMaterial || materials.Ch31_body}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={materials._shoesMaterial || materials.Ch31_body}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={materials._sweaterMaterial || materials.Ch31_body}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');
