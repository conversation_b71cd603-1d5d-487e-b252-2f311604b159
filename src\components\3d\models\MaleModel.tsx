'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;



  // Force re-render when color combination changes
  const [forceUpdate, setForceUpdate] = React.useState(0);

  // Debug logging and force update when color combination changes
  useEffect(() => {
    if (!materials || !nodes) return;

    console.log('🔄 Male model color combination updated...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    // Debug: Log all available materials and nodes
    console.log('📋 Available materials in temp.glb:');
    Object.keys(materials).forEach((materialName, index) => {
      console.log(`  ${index + 1}. "${materialName}"`);
    });

    console.log('📋 Available nodes/meshes in temp.glb:');
    Object.keys(nodes).forEach((nodeName, index) => {
      console.log(`  ${index + 1}. "${nodeName}"`);
    });

    if (colorCombination && enableColorSync) {
      console.log('🎨 Colors will be applied to:');
      console.log(`  - Sweater/Collar: ${colorCombination.shirt}`);
      console.log(`  - Pants: ${colorCombination.pants}`);
      console.log(`  - Shoes: ${colorCombination.shoes}`);

      // Force component re-render to apply new colors
      setForceUpdate(prev => prev + 1);
    }
  }, [colorCombination, enableColorSync, materials, nodes]);

  if (!nodes || !materials) {
    console.log('⏳ Male model not loaded yet...');
    return null;
  }

  // Create separate materials for each clothing piece with proper color application
  const createClothingMaterial = (baseMaterial: any, meshName: string) => {
    if (!baseMaterial) return baseMaterial;

    // Always clone the base material to create independent materials
    const newMaterial = baseMaterial.clone();

    // Ensure the material has proper properties
    newMaterial.transparent = false;
    newMaterial.opacity = 1.0;
    newMaterial.visible = true;

    // Apply colors based on exact mesh name matching
    if (colorCombination && enableColorSync) {
      let targetColor = null;

      // Exact mesh name matching for Ch31_Sweater
      if (meshName === 'Sweater' || meshName === 'Ch31_Sweater') {
        targetColor = colorCombination.shirt;
        console.log(`🎨 Applying shirt color ${targetColor} to ${meshName} (SWEATER)`);
      }
      // Exact mesh name matching for Ch31_Collar
      else if (meshName === 'Collar' || meshName === 'Ch31_Collar') {
        targetColor = colorCombination.shirt;
        console.log(`🎨 Applying shirt color ${targetColor} to ${meshName} (COLLAR)`);
      }
      // Exact mesh name matching for Ch31_Pants
      else if (meshName === 'Pants' || meshName === 'Ch31_Pants') {
        targetColor = colorCombination.pants;
        console.log(`🎨 Applying pants color ${targetColor} to ${meshName} (PANTS)`);
      }
      // Exact mesh name matching for Ch31_Shoes
      else if (meshName === 'Shoes' || meshName === 'Ch31_Shoes') {
        targetColor = colorCombination.shoes;
        console.log(`🎨 Applying shoes color ${targetColor} to ${meshName} (SHOES)`);
      }

      if (targetColor) {
        // Force color update with multiple methods
        newMaterial.color.set(targetColor);
        newMaterial.color.setHex(targetColor.replace('#', '0x'));
        newMaterial.needsUpdate = true;

        // Additional force update
        setTimeout(() => {
          newMaterial.needsUpdate = true;
        }, 100);

        console.log(`✅ Successfully applied color ${targetColor} to ${meshName}`);
        console.log(`   Material color now:`, newMaterial.color);
      } else {
        console.log(`⚠️ No color applied to ${meshName} - not a clothing item`);
      }
    } else {
      console.log(`⚠️ No color combination available for ${meshName}`);
    }

    return newMaterial;
  };

  return (
    <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]} key={`male-model-${forceUpdate}`}>
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={createClothingMaterial(materials.Ch31_body, 'Ch31_Collar')}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={createClothingMaterial(materials.Ch31_body, 'Ch31_Pants')}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={createClothingMaterial(materials.Ch31_body, 'Ch31_Shoes')}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={createClothingMaterial(materials.Ch31_body, 'Ch31_Sweater')}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');
