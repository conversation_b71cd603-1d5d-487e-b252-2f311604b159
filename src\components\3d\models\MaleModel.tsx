'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;

  // Force re-render when colors change
  const [forceUpdate, setForceUpdate] = useState(0);

  // Debug logging for props
  useEffect(() => {
    console.log('🔄 MaleModel: Props changed');
    console.log('  - colorCombination:', colorCombination);
    console.log('  - enableColorSync:', enableColorSync);
    console.log('  - materials available:', !!materials);
    console.log('  - nodes available:', !!nodes);
  }, [colorCombination, enableColorSync, materials, nodes]);

  // Create cloned materials that we can modify
  const clonedMaterials = useMemo(() => {
    if (!materials?.Ch31_body) {
      console.log('⚠️ MaleModel: Ch31_body material not available');
      return null;
    }

    console.log('✅ MaleModel: Creating cloned materials');
    return {
      sweater: materials.Ch31_body.clone(),
      collar: materials.Ch31_body.clone(),
      pants: materials.Ch31_body.clone(),
      shoes: materials.Ch31_body.clone(),
    };
  }, [materials]);

  // Apply colors when color combination changes
  useEffect(() => {
    if (!colorCombination || !enableColorSync || !clonedMaterials) return;

    console.log('🎨 Applying colors to model:', colorCombination);
    console.log('🎨 Enable color sync:', enableColorSync);
    console.log('🎨 Cloned materials available:', !!clonedMaterials);

    // Apply colors to cloned materials
    if (clonedMaterials.sweater) {
      clonedMaterials.sweater.color.set(colorCombination.shirt);
      clonedMaterials.sweater.needsUpdate = true;
      console.log(`✅ Applied shirt color ${colorCombination.shirt} to sweater material`);
    }

    if (clonedMaterials.collar) {
      clonedMaterials.collar.color.set(colorCombination.shirt);
      clonedMaterials.collar.needsUpdate = true;
      console.log(`✅ Applied shirt color ${colorCombination.shirt} to collar material`);
    }

    if (clonedMaterials.pants) {
      clonedMaterials.pants.color.set(colorCombination.pants);
      clonedMaterials.pants.needsUpdate = true;
      console.log(`✅ Applied pants color ${colorCombination.pants} to pants material`);
    }

    if (clonedMaterials.shoes) {
      clonedMaterials.shoes.color.set(colorCombination.shoes);
      clonedMaterials.shoes.needsUpdate = true;
      console.log(`✅ Applied shoes color ${colorCombination.shoes} to shoes material`);
    }

    // Force re-render to show changes
    setForceUpdate(prev => prev + 1);
  }, [colorCombination, enableColorSync, clonedMaterials]);

  if (!nodes || !materials) {
    console.log('⏳ Model not loaded yet...');
    return null;
  }

  return (
    <group
      ref={group}
      dispose={null}
      scale={[0.01, 0.01, 0.01]}
      position={[0, -1, 0]}
      rotation={[Math.PI / 2, 0, 0]}
      key={`male-model-${forceUpdate}`}
    >
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={clonedMaterials?.collar || materials.Ch31_body}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={clonedMaterials?.pants || materials.Ch31_body}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={clonedMaterials?.shoes || materials.Ch31_body}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={clonedMaterials?.sweater || materials.Ch31_body}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');