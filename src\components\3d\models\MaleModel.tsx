'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;

  // Refs for direct material access
  const sweaterRef = useRef<THREE.SkinnedMesh>(null);
  const collarRef = useRef<THREE.SkinnedMesh>(null);
  const pantsRef = useRef<THREE.SkinnedMesh>(null);
  const shoesRef = useRef<THREE.SkinnedMesh>(null);

  // Apply colors when color combination changes
  useEffect(() => {
    if (!colorCombination || !enableColorSync) return;

    console.log('🎨 Applying colors to model:', colorCombination);

    // Apply colors to each mesh directly
    if (sweaterRef.current?.material) {
      const sweaterMat = sweaterRef.current.material as THREE.MeshStandardMaterial;
      if (sweaterMat.color) {
        sweaterMat.color.set(colorCombination.shirt);
        sweaterMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${colorCombination.shirt} to sweater`);
      }
    }

    if (collarRef.current?.material) {
      const collarMat = collarRef.current.material as THREE.MeshStandardMaterial;
      if (collarMat.color) {
        collarMat.color.set(colorCombination.shirt);
        collarMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${colorCombination.shirt} to collar`);
      }
    }

    if (pantsRef.current?.material) {
      const pantsMat = pantsRef.current.material as THREE.MeshStandardMaterial;
      if (pantsMat.color) {
        pantsMat.color.set(colorCombination.pants);
        pantsMat.needsUpdate = true;
        console.log(`✅ Applied pants color ${colorCombination.pants} to pants`);
      }
    }

    if (shoesRef.current?.material) {
      const shoesMat = shoesRef.current.material as THREE.MeshStandardMaterial;
      if (shoesMat.color) {
        shoesMat.color.set(colorCombination.shoes);
        shoesMat.needsUpdate = true;
        console.log(`✅ Applied shoes color ${colorCombination.shoes} to shoes`);
      }
    }
  }, [colorCombination, enableColorSync]);

  if (!nodes || !materials) {
    console.log('⏳ Model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]}>
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            ref={collarRef}
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            ref={pantsRef}
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            ref={shoesRef}
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            ref={sweaterRef}
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');