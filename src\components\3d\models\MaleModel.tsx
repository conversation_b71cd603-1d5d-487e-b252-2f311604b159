'use client';

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface MaleModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const MaleModel: React.FC<MaleModelProps> = ({
  colorCombination,
  enableColorSync = false
}) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;

  // Debug logging for props
  useEffect(() => {
    console.log('🔄 MaleModel: Props changed');
    console.log('  - colorCombination:', colorCombination);
    console.log('  - enableColorSync:', enableColorSync);
    console.log('  - materials available:', !!materials);
    console.log('  - nodes available:', !!nodes);
  }, [colorCombination, enableColorSync, materials, nodes]);

  // Create new materials whenever color combination changes
  const finalMaterials = useMemo(() => {
    if (!colorCombination || !enableColorSync || !materials?.Ch31_body) {
      return {
        sweater: materials?.Ch31_body,
        collar: materials?.Ch31_body,
        pants: materials?.Ch31_body,
        shoes: materials?.Ch31_body,
      };
    }

    console.log('🎨 Creating new colored materials for:', colorCombination);

    // Create completely new materials with colors applied
    const sweaterMaterial = materials.Ch31_body.clone();
    sweaterMaterial.color.set(colorCombination.shirt);
    sweaterMaterial.needsUpdate = true;

    const collarMaterial = materials.Ch31_body.clone();
    collarMaterial.color.set(colorCombination.shirt);
    collarMaterial.needsUpdate = true;

    const pantsMaterial = materials.Ch31_body.clone();
    pantsMaterial.color.set(colorCombination.pants);
    pantsMaterial.needsUpdate = true;

    const shoesMaterial = materials.Ch31_body.clone();
    shoesMaterial.color.set(colorCombination.shoes);
    shoesMaterial.needsUpdate = true;

    console.log(`✅ Created new materials with colors:`);
    console.log(`  - Sweater: ${colorCombination.shirt}`);
    console.log(`  - Collar: ${colorCombination.shirt}`);
    console.log(`  - Pants: ${colorCombination.pants}`);
    console.log(`  - Shoes: ${colorCombination.shoes}`);

    return {
      sweater: sweaterMaterial,
      collar: collarMaterial,
      pants: pantsMaterial,
      shoes: shoesMaterial,
    };
  }, [colorCombination, enableColorSync, materials]);

  // Force material updates after render
  useEffect(() => {
    if (!finalMaterials || !colorCombination || !enableColorSync) return;

    // Force all materials to update their rendering
    Object.values(finalMaterials).forEach(material => {
      if (material && material.needsUpdate !== undefined) {
        material.needsUpdate = true;
      }
    });

    console.log('🔄 Forced material updates for visual refresh');
  }, [finalMaterials, colorCombination, enableColorSync]);

  if (!nodes || !materials) {
    console.log('⏳ Model not loaded yet...');
    return null;
  }

  return (
    <group
      ref={group}
      dispose={null}
      scale={[0.01, 0.01, 0.01]}
      position={[0, -1, 0]}
      rotation={[Math.PI / 2, 0, 0]}
      key={`male-model-${colorCombination?.shirt || 'default'}`}
    >
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={finalMaterials.collar}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={finalMaterials.pants}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={finalMaterials.shoes}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={finalMaterials.sweater}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');