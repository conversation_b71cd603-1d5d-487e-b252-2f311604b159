'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF } from '@react-three/drei';
import * as THREE from 'three';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
  outfitName: string;
  overallReason: string;
}

// 3D Model Component
const Model3D: React.FC<{ selectedColors: ColorCombination | null }> = ({ selectedColors }) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;
  
  // Refs for direct mesh access
  const sweaterRef = useRef<THREE.SkinnedMesh>(null);
  const collarRef = useRef<THREE.SkinnedMesh>(null);
  const pantsRef = useRef<THREE.SkinnedMesh>(null);
  const shoesRef = useRef<THREE.SkinnedMesh>(null);

  // Apply colors directly to meshes when colors change
  useEffect(() => {
    if (!selectedColors || !materials?.Ch31_body) return;

    console.log('🎨 Applying colors to model:', selectedColors);

    // Apply colors directly to mesh materials after a short delay
    setTimeout(() => {
      if (sweaterRef.current) {
        const newMaterial = materials.Ch31_body.clone();
        newMaterial.color.set(selectedColors.shirt);
        sweaterRef.current.material = newMaterial;
        console.log('✅ Applied shirt color to sweater:', selectedColors.shirt);
      }

      if (collarRef.current) {
        const newMaterial = materials.Ch31_body.clone();
        newMaterial.color.set(selectedColors.shirt);
        collarRef.current.material = newMaterial;
        console.log('✅ Applied shirt color to collar:', selectedColors.shirt);
      }

      if (pantsRef.current) {
        const newMaterial = materials.Ch31_body.clone();
        newMaterial.color.set(selectedColors.pants);
        pantsRef.current.material = newMaterial;
        console.log('✅ Applied pants color:', selectedColors.pants);
      }

      if (shoesRef.current) {
        const newMaterial = materials.Ch31_body.clone();
        newMaterial.color.set(selectedColors.shoes);
        shoesRef.current.material = newMaterial;
        console.log('✅ Applied shoes color:', selectedColors.shoes);
      }
    }, 100);
  }, [selectedColors, materials]);

  if (!nodes || !materials) {
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]}>
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            ref={collarRef}
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            ref={pantsRef}
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            ref={shoesRef}
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            ref={sweaterRef}
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Main Page Component
export const MaleModel: React.FC = () => {
  const [colorCombinations, setColorCombinations] = useState<ColorCombination[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load color recommendations from API
  useEffect(() => {
    loadColorRecommendations();
  }, []);

  const loadColorRecommendations = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('🎨 Loading color recommendations from API...');
      
      const response = await fetch('https://faceapp-ttwh.onrender.com/api/face/recommendations/latest', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ API Response:', data);

      if (data && data.recommendations && data.recommendations.length > 0) {
        const combinations: ColorCombination[] = data.recommendations.map((outfit: any, index: number) => ({
          shirt: outfit.shirt?.hex || '#3498db',
          pants: outfit.pants?.hex || '#2c3e50',
          shoes: outfit.shoes?.hex || '#000000',
          outfitName: outfit.outfitName || `Style ${index + 1}`,
          overallReason: outfit.overallReason || 'AI recommended combination'
        }));

        setColorCombinations(combinations);
        console.log('🎨 Loaded combinations:', combinations);
      } else {
        // Fallback test colors if API fails
        const testCombinations: ColorCombination[] = [
          {
            shirt: '#ff0000',
            pants: '#00ff00', 
            shoes: '#0000ff',
            outfitName: 'Test: Red Shirt',
            overallReason: 'Test combination with bright colors'
          },
          {
            shirt: '#ffff00',
            pants: '#ff00ff',
            shoes: '#00ffff',
            outfitName: 'Test: Yellow Shirt',
            overallReason: 'Extreme test combination'
          }
        ];
        setColorCombinations(testCombinations);
        console.log('🎨 Using test combinations');
      }
    } catch (err: any) {
      console.error('❌ Error loading recommendations:', err);
      
      // Fallback test colors
      const testCombinations: ColorCombination[] = [
        {
          shirt: '#ff0000',
          pants: '#00ff00', 
          shoes: '#0000ff',
          outfitName: 'Test: Red Shirt',
          overallReason: 'Test combination with bright colors'
        },
        {
          shirt: '#ffff00',
          pants: '#ff00ff',
          shoes: '#00ffff',
          outfitName: 'Test: Yellow Shirt',
          overallReason: 'Extreme test combination'
        }
      ];
      setColorCombinations(testCombinations);
      setError('Using test colors - API failed');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedColors = colorCombinations[selectedIndex] || null;

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900 flex">
      {/* 3D Model Area */}
      <div className="flex-1 relative">
        {/* Header */}
        <div className="absolute top-0 left-0 right-0 z-10 p-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            🎨 AI Color Recommendations
          </h1>
          <p className="text-purple-200">
            Click color combinations to apply them to the 3D model
          </p>
          {selectedColors && (
            <div className="mt-4 p-3 bg-black/30 rounded-lg">
              <p className="text-white font-semibold">{selectedColors.outfitName}</p>
              <p className="text-white/80 text-sm">
                Shirt: {selectedColors.shirt} | Pants: {selectedColors.pants} | Shoes: {selectedColors.shoes}
              </p>
            </div>
          )}
        </div>

        {/* Extreme Test Button - In Model Screen */}
        <div className="absolute bottom-6 left-6 z-10">
          <button
            onClick={() => {
              const extremeColors = {
                shirt: '#ffff00',
                pants: '#ff00ff',
                shoes: '#00ffff',
                outfitName: '⚡ EXTREME TEST',
                overallReason: 'Bright test colors to verify the system works'
              };
              setColorCombinations([extremeColors, ...colorCombinations]);
              setSelectedIndex(0);
              console.log('⚡ Applied extreme test colors');
            }}
            className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-pink-500 text-white font-semibold rounded-xl hover:from-yellow-600 hover:to-pink-600 transition-all duration-300 shadow-lg"
          >
            ⚡ Test Extreme Colors
          </button>
        </div>

        {/* 3D Canvas */}
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100vh' }}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          <Model3D selectedColors={selectedColors} />

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
          />
        </Canvas>
      </div>

      {/* Color Combinations Panel */}
      <div className="w-96 bg-black/40 backdrop-blur-2xl border-l border-white/10 flex flex-col">
        <div className="p-6 border-b border-white/10">
          <h3 className="text-xl font-bold text-white mb-2">Color Combinations</h3>
          <p className="text-purple-200 text-sm">
            {colorCombinations.length} available combinations
          </p>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Loading combinations...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-4 mb-4 bg-red-500/20 rounded-lg">
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          )}

          <div className="space-y-4">
            {colorCombinations.map((combination, index) => (
              <div
                key={index}
                onClick={() => {
                  setSelectedIndex(index);
                  console.log('🎨 Selected combination:', combination);
                }}
                className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                  selectedIndex === index
                    ? 'bg-gradient-to-r from-purple-500/30 to-pink-500/30 border-2 border-purple-400 shadow-lg'
                    : 'bg-white/10 hover:bg-white/20 border-2 border-transparent'
                }`}
              >
                <h4 className="text-white font-semibold mb-3">{combination.outfitName}</h4>
                
                {/* Color Preview */}
                <div className="flex gap-3 mb-3">
                  <div className="flex flex-col items-center">
                    <div
                      className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                      style={{ backgroundColor: combination.shirt }}
                    ></div>
                    <span className="text-xs text-white/70 mt-1">Shirt</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div
                      className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                      style={{ backgroundColor: combination.pants }}
                    ></div>
                    <span className="text-xs text-white/70 mt-1">Pants</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div
                      className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                      style={{ backgroundColor: combination.shoes }}
                    ></div>
                    <span className="text-xs text-white/70 mt-1">Shoes</span>
                  </div>
                </div>

                <p className="text-white/80 text-sm leading-relaxed">{combination.overallReason}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');
