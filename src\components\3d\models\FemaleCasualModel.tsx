'use client';

import React, { useRef, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';
import { Color } from 'three';
import * as THREE from 'three';

interface FemaleCasualModelProps {
  colorCombination?: {
    shirt: string;
    pants: string;
    shoes: string;
  } | null;
  enableColorSync?: boolean;
}

export const FemaleCasualModel: React.FC<FemaleCasualModelProps> = ({ 
  colorCombination, 
  enableColorSync = false 
}) => {
  const group = useRef<THREE.Group>(null);
  const { scene: casualGltf } = useGLTF('/casualwear.glb') as any;

  // Color mapping for casual wear
  const casualColorMap = {
    skin: new Color(0xfdbcb4), // Natural skin tone
    hair: new Color(0x4a2c17), // Dark brown hair
    shirt: colorCombination ? new Color(colorCombination.shirt) : new Color(0xe11d48), // Red shirt default
    pants: colorCombination ? new Color(colorCombination.pants) : new Color(0x1e40af), // Blue pants default
    shoes: colorCombination ? new Color(colorCombination.shoes) : new Color(0x1f2937), // Dark shoes default
    belt: new Color(0x000000), // Black belt
    accessories: new Color(0x8B4513), // Brown accessories
    clothing: new Color(0x6b7280) // Default gray clothing
  };

  // Apply colors to casual model
  useEffect(() => {
    if (!casualGltf) return;

    console.log('🔄 Updating female casual model colors...');
    console.log('Color combination:', colorCombination);
    console.log('Enable color sync:', enableColorSync);

    let updatedMeshes = 0;

    // Update color map if color combination is provided
    if (colorCombination && enableColorSync) {
      casualColorMap.shirt = new Color(colorCombination.shirt);
      casualColorMap.pants = new Color(colorCombination.pants);
      casualColorMap.shoes = new Color(colorCombination.shoes);
    }

    casualGltf.traverse((child: any) => {
      if (child.isMesh && child.name) {
        const meshName = child.name.toLowerCase();
        let targetColor = casualColorMap.clothing; // default

        // Determine color based on mesh name
        if (meshName === 'face' || meshName === 'hand' || meshName === 'hand001' ||
            meshName === 'leg' || meshName === 'leg001') {
          targetColor = casualColorMap.skin;
        } else if (meshName === 'hair') {
          targetColor = casualColorMap.hair;
        } else if (meshName === 'jacket' || meshName === 'underjacket' || meshName === 'neckcloth' ||
                   meshName === 'jackenthand' || meshName === 'jackethand') {
          targetColor = casualColorMap.shirt;
        } else if (meshName === 'pant' || meshName === 'pant001') {
          targetColor = casualColorMap.pants;
        } else if (meshName.includes('boot')) {
          targetColor = casualColorMap.shoes;
        } else if (meshName === 'belt') {
          targetColor = casualColorMap.belt; // Black belt
        } else if (meshName.includes('bag') || meshName === 'baghand') {
          targetColor = casualColorMap.accessories;
        }

        // Apply color to mesh material
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat: any) => {
              if (mat.color) {
                mat.color.copy(targetColor);
                // Ensure material properties are preserved
                mat.transparent = mat.transparent || false;
                mat.opacity = mat.opacity || 1.0;
                mat.visible = true;
                mat.needsUpdate = true;
              }
            });
          } else if (child.material.color) {
            child.material.color.copy(targetColor);
            // Ensure material properties are preserved
            child.material.transparent = child.material.transparent || false;
            child.material.opacity = child.material.opacity || 1.0;
            child.material.visible = true;
            child.material.needsUpdate = true;
          }
          updatedMeshes++;
        }
      }
    });

    console.log(`✅ Updated ${updatedMeshes} meshes on female casual model`);
  }, [colorCombination, enableColorSync, casualGltf]);

  if (!casualGltf) {
    console.log('⏳ Female casual model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[1, 1, 1]} position={[0, -1, 0]}>
      <primitive object={casualGltf} />
    </group>
  );
};

// Preload the model
useGLTF.preload('/casualwear.glb');
