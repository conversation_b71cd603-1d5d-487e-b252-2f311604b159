'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { <PERSON>rk<PERSON>, <PERSON>lette, Zap, Star, RefreshCw, Eye, Heart, ArrowRight, ChevronRight, ChevronLeft, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);
  const [showFacialAnalysis, setShowFacialAnalysis] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Set default model based on user gender following the exact workflow
  useEffect(() => {
    console.log('🚻 Setting model based on user gender:', user?.gender);

    if (user?.gender === 'female') {
      // For female users, default to casual (can be changed to formal later)
      setSelectedModel('female-casual');
      console.log('👩 Female user detected - setting casual model as default');
    } else {
      // For male users, use the temp.glb model (explore.tsx equivalent)
      setSelectedModel('male');
      console.log('👨 Male user detected - setting male model (temp.glb)');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update colors when outfit selection changes
  useEffect(() => {
    const outfits = recommendations?.outfits || recommendations?.recommendations || [];
    if (outfits && outfits[selectedOutfitIndex]) {
      const selectedOutfit = outfits[selectedOutfitIndex];
      console.log('🎨 Applying colors from outfit:', selectedOutfit);

      setColorCombination({
        shirt: selectedOutfit.shirt.hex,
        pants: selectedOutfit.pants.hex,
        shoes: selectedOutfit.shoes?.hex || selectedOutfit.pants.hex
      });

      toast.success(`Applied ${selectedOutfit.outfitName} colors!`);
    }
  }, [selectedOutfitIndex, recommendations]);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    console.log('🔄 Color combination effect triggered:', {
      hasRecommendations: !!recommendations,
      outfitsLength: recommendations?.outfits?.length || 0,
      selectedOutfitIndex,
      selectedOutfit: recommendations?.outfits?.[selectedOutfitIndex]
    });

    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      const newColorCombination = {
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      };

      console.log('🎨 ModelViewer: Setting new color combination:', newColorCombination);
      console.log('🎨 ModelViewer: Selected model:', selectedModel);
      console.log('🎨 ModelViewer: Outfit details:', outfit);

      setColorCombination(newColorCombination);

      // Show success toast when colors are applied
      toast.success(`Applied ${outfit.outfitName || 'color combination'} colors!`, {
        duration: 2000,
        icon: '🎨'
      });
    } else {
      console.log('🎨 ModelViewer: No valid outfit found, clearing color combination');
      console.log('🎨 ModelViewer: Debug info:', {
        recommendations,
        selectedOutfitIndex,
        outfits: recommendations?.outfits
      });
      setColorCombination(null);
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Fetching latest color recommendations from API...');

      // Use the API method with automatic cookie-based authentication
      const response = await recommendationAPI.getLatestRecommendation();
      console.log('✅ Latest recommendations API response:', response);

      if (response.success && response.data) {
        // Handle the API response structure
        const recommendationData = response.data;

        setRecommendations(recommendationData);
        setSelectedOutfitIndex(0); // Auto-select first outfit
        console.log('🎨 Color recommendations loaded successfully:', response.data);
        console.log('📋 Outfits array:', response.data.outfits);
        toast.success('Latest color recommendations loaded!');
      } else {
        console.log('⚠️ No recommendations found in response');
        toast.error('No color recommendations found. Please complete a face analysis first.');
      }
    } catch (error: any) {
      console.error('💥 Failed to fetch latest recommendations:', error);

      // Handle specific error cases
      if (error.response?.status === 401) {
        toast.error('Please log in to view recommendations.');
      } else if (error.response?.status === 404) {
        toast.error('No color recommendations found. Please complete a face analysis first.');
      } else {
        toast.error('Failed to load recommendations. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };



  const handleOutfitChange = (index: number) => {
    console.log('👆 User clicked outfit at index:', index);
    console.log('👆 Available outfits:', recommendations?.outfits?.length || 0);
    console.log('👆 Selected outfit:', recommendations?.outfits?.[index]);

    setSelectedOutfitIndex(index);
    setIsMobileMenuOpen(false); // Close mobile menu when outfit is selected
    console.log('✅ ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: !!colorCombination
    };

    console.log('🎭 ModelViewer: Rendering model:', selectedModel, 'with props:', modelProps);

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };



  return (
    <div className="w-full min-h-screen relative bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900">
      {/* Mobile Menu Toggle Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="fixed top-4 right-4 z-50 lg:hidden bg-white/20 backdrop-blur-md rounded-2xl p-3 border border-white/30 shadow-lg"
      >
        {isMobileMenuOpen ? <X className="w-6 h-6 text-white" /> : <Palette className="w-6 h-6 text-white" />}
      </button>

      {/* Right Side Panel - Color Recommendations */}
      <div className={`fixed right-0 top-0 bottom-0 z-40 w-full lg:w-96 bg-black/40 backdrop-blur-2xl border-l border-white/10 transform transition-transform duration-300 ${
        isMobileMenuOpen || window.innerWidth >= 1024 ? 'translate-x-0' : 'translate-x-full'
      } lg:translate-x-0`}>
        
        {/* Applied Colors Display */}
        {colorCombination && (
          <div className="p-6 border-b border-white/10">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-3">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-bold text-white mb-1">Applied Colors</h4>
              <p className="text-xs text-purple-200">Currently shown on model</p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.shirt }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Shirt</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.shirt}</span>
              </div>

              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.pants }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Pants</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.pants}</span>
              </div>

              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.shoes }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Shoes</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.shoes}</span>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-white/20">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-300 font-medium">Colors Applied Successfully</span>
              </div>
            </div>
          </div>
        )}

        {/* Color Recommendations */}
        {recommendations && recommendations.outfits && (
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">AI Recommendations</h3>
                  <p className="text-purple-200 text-sm">{recommendations.outfits.length} outfit combinations</p>
                </div>
              </div>

              {/* AI Details */}
              <div className="grid grid-cols-1 gap-2 mb-6">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                  <span className="text-purple-200 text-xs">AI Service: </span>
                  <span className="text-white font-medium text-xs">{recommendations.aiService}</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl px-3 py-2 border border-white/20">
                  <span className="text-purple-200 text-xs">Processing: </span>
                  <span className="text-white font-medium text-xs">{recommendations.processingTime}ms</span>
                </div>
                <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-xl px-3 py-2 border border-green-400/30">
                  <span className="text-green-200 text-xs">Confidence: </span>
                  <span className="text-green-100 font-bold text-xs">{Math.round(recommendations.confidence * 100)}%</span>
                </div>
              </div>

              {/* Outfit List */}
              <div className="space-y-4">
                {recommendations.outfits.map((outfit, index) => (
                  <button
                    key={index}
                    onClick={() => handleOutfitChange(index)}
                    className={`w-full p-4 rounded-2xl transition-all duration-300 text-left ${
                      selectedOutfitIndex === index
                        ? 'bg-gradient-to-br from-purple-500/30 to-pink-500/30 border-2 border-purple-400/50 shadow-lg'
                        : 'bg-white/10 border-2 border-white/20 hover:bg-white/20 hover:border-white/30'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex gap-1.5 mt-1">
                        <div
                          className="w-4 h-4 rounded-full border border-white/40 shadow-sm"
                          style={{ backgroundColor: outfit.shirt.hex }}
                        />
                        <div
                          className="w-4 h-4 rounded-full border border-white/40 shadow-sm"
                          style={{ backgroundColor: outfit.pants.hex }}
                        />
                        {outfit.shoes && (
                          <div
                            className="w-4 h-4 rounded-full border border-white/40 shadow-sm"
                            style={{ backgroundColor: outfit.shoes.hex }}
                          />
                        )}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-white mb-1 text-sm">{outfit.outfitName}</h4>
                        <p className="text-xs text-purple-200 leading-relaxed">{outfit.overallReason}</p>
                        {selectedOutfitIndex === index && (
                          <div className="mt-2 pt-2 border-t border-white/20">
                            <div className="flex items-center gap-2">
                              <Star className="w-3 h-3 text-yellow-400" />
                              <span className="text-xs text-yellow-300 font-medium">Currently Applied</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <ChevronRight className="w-4 h-4 text-white/60 mt-1" />
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* No Recommendations State */}
        {!recommendations && !isLoading && (
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="text-center">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-16 h-16 mx-auto flex items-center justify-center">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">No Recommendations</h3>
              <p className="text-purple-200 mb-4 text-sm leading-relaxed">
                Complete face analysis to see personalized color recommendations here.
              </p>
              <div className="space-y-3">
                <button
                  onClick={fetchLatestRecommendations}
                  className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold text-sm hover:shadow-lg transition-all duration-300"
                >
                  <RefreshCw className="w-4 h-4 inline mr-2" />
                  Load Latest
                </button>
                <a
                  href="/dashboard/new-analysis"
                  className="w-full px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-xl font-semibold text-sm hover:shadow-lg transition-all duration-300 block text-center"
                >
                  <Sparkles className="w-4 h-4 inline mr-2" />
                  Start Analysis
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main 3D Canvas */}
      <div className="fixed inset-0 z-0 lg:pr-96">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
          dpr={[1, 2]}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          {renderModel()}

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
            touches={{
              ONE: 2, // ROTATE
              TWO: 1  // DOLLY (zoom)
            }}
          />
        </Canvas>
      </div>

      {/* Debug Info - Remove in production */}
      {colorCombination && (
        <div className="fixed top-4 left-4 z-50 bg-black/80 text-white p-2 rounded text-xs">
          <div>Colors Active:</div>
          <div>Shirt: {colorCombination.shirt}</div>
          <div>Pants: {colorCombination.pants}</div>
          <div>Shoes: {colorCombination.shoes}</div>
          <div>Model: {selectedModel}</div>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-xl flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20 text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
                <div className="absolute inset-4 border-4 border-transparent border-t-blue-500 rounded-full animate-spin animation-delay-300"></div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-white">Analyzing Colors</h3>
              <p className="text-purple-200">AI is crafting your perfect palette...</p>
              <div className="flex justify-center space-x-1 mt-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce animation-delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  );
};