'use client';

import React, { useState, useEffect } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import { <PERSON>rk<PERSON>, <PERSON>lette, Star, Refresh<PERSON><PERSON>, Eye, Heart, ArrowRight, ChevronRight, ChevronLeft, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationAPI } from '@/lib/api';
import { ColorRecommendation } from '@/types';
import { MaleModel } from './models/MaleModel';
import { FemaleModel } from './models/FemaleModel';
import { FemaleCasualModel } from './models/FemaleCasualModel';
import { FemaleFormalModel } from './models/FemaleFormalModel';
import { FemaleCheongsam } from './models/FemaleCheongsam';
import { toast } from 'react-hot-toast';

interface ModelViewerProps {
  analysisId?: string;
}

type ModelType = 'male' | 'female-casual' | 'female-formal' | 'female-cheongsam';

interface ColorCombination {
  shirt: string;
  pants: string;
  shoes: string;
}

export const ModelViewer: React.FC<ModelViewerProps> = ({ analysisId }) => {
  const { user } = useAuth();
  const [selectedModel, setSelectedModel] = useState<ModelType>('male');
  const [recommendations, setRecommendations] = useState<ColorRecommendation | null>(null);
  const [selectedOutfitIndex, setSelectedOutfitIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [colorCombination, setColorCombination] = useState<ColorCombination | null>(null);
  const [showFacialAnalysis, setShowFacialAnalysis] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Handle responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Set default model based on user gender following the exact workflow
  useEffect(() => {
    console.log('🚻 Setting model based on user gender:', user?.gender);

    if (user?.gender === 'female') {
      // For female users, default to casual (can be changed to formal later)
      setSelectedModel('female-casual');
      console.log('👩 Female user detected - setting casual model as default');
    } else {
      // For male users, use the temp.glb model (explore.tsx equivalent)
      setSelectedModel('male');
      console.log('👨 Male user detected - setting male model (temp.glb)');
    }
  }, [user?.gender]);

  // Fetch latest recommendations on component mount
  useEffect(() => {
    fetchLatestRecommendations();
  }, []);

  // Update color combination when recommendations or outfit selection changes
  useEffect(() => {
    console.log('🔄 Color combination effect triggered:', {
      hasRecommendations: !!recommendations,
      outfitsLength: recommendations?.outfits?.length || 0,
      selectedOutfitIndex,
      selectedOutfit: recommendations?.outfits?.[selectedOutfitIndex]
    });

    if (recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex]) {
      const outfit = recommendations.outfits[selectedOutfitIndex];
      const newColorCombination = {
        shirt: outfit.shirt.hex,
        pants: outfit.pants.hex,
        shoes: outfit.shoes?.hex || '#8B4513'
      };

      console.log('🎨 ModelViewer: Setting new color combination:', newColorCombination);
      console.log('🎨 ModelViewer: Selected model:', selectedModel);
      console.log('🎨 ModelViewer: Outfit details:', outfit);

      setColorCombination(newColorCombination);

      // Show success toast when colors are applied
      toast.success(`Applied ${outfit.outfitName || 'color combination'} colors!`, {
        duration: 2000,
        icon: '🎨'
      });
    } else {
      console.log('🎨 ModelViewer: No valid outfit found, clearing color combination');
      console.log('🎨 ModelViewer: Debug info:', {
        recommendations,
        selectedOutfitIndex,
        outfits: recommendations?.outfits
      });
      setColorCombination(null);
    }
  }, [recommendations, selectedOutfitIndex]);

  const fetchLatestRecommendations = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Fetching latest color recommendations from API...');

      // Use the API method with automatic cookie-based authentication
      const response = await recommendationAPI.getLatestRecommendation();
      console.log('✅ Latest recommendations API response:', response);

      if (response.success && response.data) {
        console.log('🔍 Full API response structure:', response);
        console.log('🔍 Response.data:', response.data);

        // Cast to any to handle nested structure
        const responseData = response.data as any;
        console.log('🔍 Response.data.data:', responseData.data);

        // Handle the nested API response structure: response.data.data.recommendations
        let recommendationData = null;

        if (responseData.data && responseData.data.recommendations) {
          // New structure: response.data.data.recommendations
          recommendationData = {
            ...responseData.data,
            outfits: responseData.data.recommendations // Map recommendations to outfits
          };
          console.log('✅ Extracted recommendations from nested structure:', recommendationData);
        } else if (responseData.recommendations) {
          // Fallback: response.data.recommendations
          recommendationData = {
            ...responseData,
            outfits: responseData.recommendations
          };
          console.log('✅ Extracted recommendations from direct structure:', recommendationData);
        } else if (responseData.outfits) {
          // Fallback: response.data.outfits
          recommendationData = responseData;
          console.log('✅ Using existing outfits structure:', recommendationData);
        }

        if (recommendationData && recommendationData.outfits && recommendationData.outfits.length > 0) {
          setRecommendations(recommendationData);
          setSelectedOutfitIndex(0); // Auto-select first outfit
          console.log('🎨 Color recommendations loaded successfully:', recommendationData);
          console.log('📋 Recommendations array:', recommendationData.outfits);
          console.log('📋 First outfit:', recommendationData.outfits[0]);
          toast.success(`Loaded ${recommendationData.outfits.length} color combinations!`);
        } else {
          console.log('⚠️ No recommendations found in response structure');
          console.log('⚠️ Available keys in response.data:', Object.keys(responseData));
          toast.error('No color recommendations found. Please complete a face analysis first.');
        }
      } else {
        console.log('⚠️ API response failed');
        toast.error('Failed to fetch recommendations. Please try again.');
      }
    } catch (error: any) {
      console.error('💥 Failed to fetch latest recommendations:', error);

      // Handle specific error cases
      if (error.response?.status === 401) {
        toast.error('Please log in to view recommendations.');
      } else if (error.response?.status === 404) {
        toast.error('No color recommendations found. Please complete a face analysis first.');
      } else {
        toast.error('Failed to load recommendations. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };



  const handleOutfitChange = (index: number) => {
    console.log('👆 User clicked outfit at index:', index);
    console.log('👆 Available outfits:', recommendations?.outfits?.length || 0);
    console.log('👆 Selected outfit:', recommendations?.outfits?.[index]);

    setSelectedOutfitIndex(index);
    setIsMobileMenuOpen(false); // Close mobile menu when outfit is selected
    console.log('✅ ModelViewer: Outfit changed to index:', index);
  };

  const renderModel = () => {
    const modelProps = {
      colorCombination,
      enableColorSync: true // Always enable color sync when we have a color combination
    };

    console.log('🎭 ModelViewer: Rendering model:', selectedModel, 'with props:', modelProps);
    console.log('🎨 Color combination being passed:', colorCombination);

    switch (selectedModel) {
      case 'male':
        return <MaleModel {...modelProps} />;
      case 'female-casual':
        return <FemaleCasualModel {...modelProps} />;
      case 'female-formal':
        return <FemaleFormalModel {...modelProps} />;
      case 'female-cheongsam':
        return <FemaleCheongsam {...modelProps} />;
      default:
        return <MaleModel {...modelProps} />;
    }
  };



  return (
    <div className="w-full min-h-screen relative bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900">

      {/* Mobile Menu Toggle Button */}
      {isMobile && (
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className={`fixed top-4 right-4 z-50 p-3 rounded-2xl backdrop-blur-xl border-2 transition-all duration-300 ${
            isMobileMenuOpen
              ? 'bg-red-500/20 border-red-400/50 text-red-300'
              : 'bg-purple-500/20 border-purple-400/50 text-purple-300'
          }`}
        >
          {isMobileMenuOpen ? (
            <X className="w-6 h-6" />
          ) : (
            <Palette className="w-6 h-6" />
          )}
        </button>
      )}

      {/* Mobile Color Preview Bar (when menu is closed) */}
      {isMobile && !isMobileMenuOpen && colorCombination && (
        <div className="fixed top-4 left-4 right-20 z-40 bg-black/60 backdrop-blur-xl rounded-2xl p-3 border border-white/20">
          <div className="flex items-center gap-3">
            <div className="flex gap-2">
              <div className="w-6 h-6 rounded-lg border border-white/40" style={{ backgroundColor: colorCombination.shirt }}></div>
              <div className="w-6 h-6 rounded-lg border border-white/40" style={{ backgroundColor: colorCombination.pants }}></div>
              <div className="w-6 h-6 rounded-lg border border-white/40" style={{ backgroundColor: colorCombination.shoes }}></div>
            </div>
            <div className="flex-1">
              <div className="text-xs text-white font-medium">Colors Applied</div>
              {recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex] && (
                <div className="text-xs text-white/60">{recommendations.outfits[selectedOutfitIndex].outfitName}</div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Right Side Panel - Color Recommendations */}
      <div className={`fixed right-0 top-0 bottom-0 z-40 w-full lg:w-96 bg-black/40 backdrop-blur-2xl border-l border-white/10 transform transition-transform duration-300 ${
        isMobileMenuOpen || !isMobile ? 'translate-x-0' : 'translate-x-full'
      } lg:translate-x-0`}>

        {/* Mobile Header with Close Button */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-white/10 lg:hidden">
            <h3 className="text-lg font-bold text-white">Color Combinations</h3>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors"
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>
        )}

        {/* Applied Colors Display */}
        {colorCombination && (
          <div className="p-6 border-b border-white/10">
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-3">
                <Palette className="w-6 h-6 text-white" />
              </div>
              <h4 className="text-lg font-bold text-white mb-1">Applied Colors</h4>
              <p className="text-xs text-purple-200">Currently shown on model</p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.shirt }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Shirt</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.shirt}</span>
              </div>

              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.pants }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Pants</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.pants}</span>
              </div>

              <div className="text-center">
                <div
                  className="w-16 h-16 rounded-2xl border-3 border-white/30 shadow-lg mx-auto mb-2 relative overflow-hidden"
                  style={{ backgroundColor: colorCombination.shoes }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                <span className="text-xs text-white/90 font-medium block">Shoes</span>
                <span className="text-xs text-white/60 font-mono">{colorCombination.shoes}</span>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-white/20">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-300 font-medium">Colors Applied Successfully</span>
              </div>
            </div>
          </div>
        )}

        {/* Color Recommendations */}
        {recommendations && recommendations.outfits && (
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                  <Palette className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">🎨 Color Combinations</h3>
                  <p className="text-purple-200 text-sm">{recommendations.outfits.length} AI-generated outfits • Click to apply</p>
                </div>
              </div>

              {/* Quick Color Preview - All 3 Combinations */}
              <div className="grid grid-cols-3 gap-2 mb-6">
                {recommendations.outfits.map((outfit, index) => (
                  <button
                    key={`preview-${index}`}
                    onClick={() => handleOutfitChange(index)}
                    className={`p-2 rounded-xl transition-all duration-300 ${
                      selectedOutfitIndex === index
                        ? 'bg-green-500/20 border-2 border-green-400/50'
                        : 'bg-white/10 border-2 border-white/20 hover:bg-white/20'
                    }`}
                  >
                    <div className="flex justify-center gap-1 mb-1">
                      <div className="w-4 h-4 rounded-full border border-white/40" style={{ backgroundColor: outfit.shirt.hex }}></div>
                      <div className="w-4 h-4 rounded-full border border-white/40" style={{ backgroundColor: outfit.pants.hex }}></div>
                      <div className="w-4 h-4 rounded-full border border-white/40" style={{ backgroundColor: outfit.shoes?.hex }}></div>
                    </div>
                    <div className="text-xs text-white/80 font-medium text-center">#{index + 1}</div>
                    {selectedOutfitIndex === index && (
                      <div className="text-xs text-green-300 text-center mt-1">✓ Active</div>
                    )}
                  </button>
                ))}
              </div>



              {/* Outfit List - All 3 Color Combinations */}
              <div className="space-y-4">

                {recommendations.outfits.map((outfit, index) => (
                  <button
                    key={index}
                    onClick={() => handleOutfitChange(index)}
                    className={`w-full p-5 rounded-3xl transition-all duration-300 text-left transform hover:scale-[1.02] ${
                      selectedOutfitIndex === index
                        ? 'bg-gradient-to-br from-purple-500/40 to-pink-500/40 border-2 border-purple-400/70 shadow-2xl ring-2 ring-purple-400/30'
                        : 'bg-white/10 border-2 border-white/20 hover:bg-white/20 hover:border-white/40 hover:shadow-xl'
                    }`}
                  >
                    <div className="flex items-start gap-4">
                      {/* Large Color Preview */}
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-xl border-3 border-white/50 shadow-xl hover:scale-110 transition-transform duration-200"
                            style={{ backgroundColor: outfit.shirt.hex }}
                            title={`Shirt: ${outfit.shirt.color} (${outfit.shirt.hex})`}
                          />
                          <span className="text-xs text-white/80 font-medium">Shirt</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 rounded-xl border-3 border-white/50 shadow-xl hover:scale-110 transition-transform duration-200"
                            style={{ backgroundColor: outfit.pants.hex }}
                            title={`Pants: ${outfit.pants.color} (${outfit.pants.hex})`}
                          />
                          <span className="text-xs text-white/80 font-medium">Pants</span>
                        </div>
                        {outfit.shoes && (
                          <div className="flex items-center gap-2">
                            <div
                              className="w-8 h-8 rounded-xl border-3 border-white/50 shadow-xl hover:scale-110 transition-transform duration-200"
                              style={{ backgroundColor: outfit.shoes.hex }}
                              title={`Shoes: ${outfit.shoes.color} (${outfit.shoes.hex})`}
                            />
                            <span className="text-xs text-white/80 font-medium">Shoes</span>
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-bold text-white text-base">{outfit.outfitName}</h4>
                          {selectedOutfitIndex === index && (
                            <div className="px-2 py-1 bg-green-500/20 rounded-full border border-green-400/30">
                              <span className="text-xs text-green-300 font-medium">✓ Applied</span>
                            </div>
                          )}
                        </div>

                        <p className="text-xs text-purple-200 leading-relaxed mb-3">{outfit.overallReason}</p>

                        {/* Color Names with Hex Codes */}
                        <div className="space-y-1 mb-3">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: outfit.shirt.hex }}></div>
                            <span className="text-xs text-white font-medium">{outfit.shirt.color}</span>
                            <span className="text-xs text-white/60 font-mono">{outfit.shirt.hex}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: outfit.pants.hex }}></div>
                            <span className="text-xs text-white font-medium">{outfit.pants.color}</span>
                            <span className="text-xs text-white/60 font-mono">{outfit.pants.hex}</span>
                          </div>
                          {outfit.shoes && (
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: outfit.shoes.hex }}></div>
                              <span className="text-xs text-white font-medium">{outfit.shoes.color}</span>
                              <span className="text-xs text-white/60 font-mono">{outfit.shoes.hex}</span>
                            </div>
                          )}
                        </div>

                        {/* Click to Apply Hint */}
                        {selectedOutfitIndex !== index && (
                          <div className="mt-2 pt-2 border-t border-white/10">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                              <span className="text-xs text-blue-300 font-medium">Click to apply to model</span>
                            </div>
                          </div>
                        )}

                        {selectedOutfitIndex === index && (
                          <div className="mt-2 pt-2 border-t border-green-400/20">
                            <div className="flex items-center gap-2">
                              <Star className="w-3 h-3 text-yellow-400" />
                              <span className="text-xs text-yellow-300 font-medium">Currently applied to 3D model</span>
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-center gap-2">
                        <div className="text-center">
                          <div className="text-xs text-white/60 font-bold">#{index + 1}</div>
                          <ChevronRight className={`w-5 h-5 transition-colors duration-300 ${
                            selectedOutfitIndex === index ? 'text-green-400' : 'text-white/60'
                          }`} />
                        </div>
                        {selectedOutfitIndex === index && (
                          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg"></div>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Debug Information */}
        <div className="p-4 bg-yellow-500/10 border-t border-yellow-500/20">
          <div className="text-xs space-y-1">
            <div className="text-yellow-200">
              <strong>Debug Info:</strong>
            </div>
            <div className="text-white">
              Has Recommendations: {recommendations ? 'Yes ✅' : 'No ❌'}
            </div>
            <div className="text-white">
              Outfits Count: {recommendations?.outfits?.length || 0}
            </div>
            <div className="text-white">
              Selected Index: {selectedOutfitIndex}
            </div>
            <div className="text-white">
              Is Loading: {isLoading ? 'Yes' : 'No'}
            </div>
            <div className="text-white">
              Color Combination: {colorCombination ? 'Applied ✅' : 'None ❌'}
            </div>
            <button
              onClick={fetchLatestRecommendations}
              className="mt-2 px-3 py-1 bg-yellow-500 text-black rounded text-xs font-medium hover:bg-yellow-400 transition-colors"
            >
              🔄 Fetch Recommendations
            </button>
          </div>
        </div>

        {/* No Recommendations State */}
        {!recommendations && !isLoading && (
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="text-center">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl w-16 h-16 mx-auto flex items-center justify-center">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>
              <h3 className="text-lg font-bold text-white mb-3">No Recommendations</h3>
              <p className="text-purple-200 mb-4 text-sm leading-relaxed">
                Complete face analysis to see personalized color recommendations here.
              </p>
              <div className="space-y-3">
                <button
                  onClick={fetchLatestRecommendations}
                  className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold text-sm hover:shadow-lg transition-all duration-300"
                >
                  <RefreshCw className="w-4 h-4 inline mr-2" />
                  Load Latest
                </button>
                <a
                  href="/dashboard/new-analysis"
                  className="w-full px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-xl font-semibold text-sm hover:shadow-lg transition-all duration-300 block text-center"
                >
                  <Sparkles className="w-4 h-4 inline mr-2" />
                  Start Analysis
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main 3D Canvas */}
      <div className="fixed inset-0 z-0 lg:pr-96">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
          dpr={[1, 2]}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          {renderModel()}

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
            touches={{
              ONE: 2, // ROTATE
              TWO: 1  // DOLLY (zoom)
            }}
          />
        </Canvas>
      </div>

      {/* Right Side Color Stack Display */}
      {colorCombination && (
        <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-20 animate-slide-in-right">
          {/* Color Stack Container */}
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-4 shadow-2xl border border-white/20 min-w-[120px] hover:bg-white/15 transition-all duration-300">
            {/* Header */}
            <div className="text-center mb-4">
              <div className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mb-2">
                <Palette className="w-5 h-5 text-white" />
              </div>
              <h4 className="text-xs font-bold text-white/90 uppercase tracking-wider">Applied Colors</h4>
            </div>

            {/* Color Stack */}
            <div className="space-y-3">
              {/* Shirt Color */}
              <div className="group cursor-pointer">
                <div className="relative">
                  <div
                    className="w-16 h-16 rounded-2xl border-3 border-white/40 shadow-xl group-hover:scale-110 transition-all duration-300 mx-auto"
                    style={{ backgroundColor: colorCombination.shirt }}
                  />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse border-2 border-white/50"></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-white/80 font-medium block">Shirt</span>
                  <span className="text-xs text-white/60 font-mono">{colorCombination.shirt}</span>
                </div>
              </div>

              {/* Pants Color */}
              <div className="group cursor-pointer">
                <div className="relative">
                  <div
                    className="w-16 h-16 rounded-2xl border-3 border-white/40 shadow-xl group-hover:scale-110 transition-all duration-300 mx-auto"
                    style={{ backgroundColor: colorCombination.pants }}
                  />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-pulse border-2 border-white/50" style={{ animationDelay: '0.5s' }}></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-white/80 font-medium block">Pants</span>
                  <span className="text-xs text-white/60 font-mono">{colorCombination.pants}</span>
                </div>
              </div>

              {/* Shoes Color */}
              <div className="group cursor-pointer">
                <div className="relative">
                  <div
                    className="w-16 h-16 rounded-2xl border-3 border-white/40 shadow-xl group-hover:scale-110 transition-all duration-300 mx-auto"
                    style={{ backgroundColor: colorCombination.shoes }}
                  />
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-full animate-pulse border-2 border-white/50" style={{ animationDelay: '1s' }}></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-xs text-white/80 font-medium block">Shoes</span>
                  <span className="text-xs text-white/60 font-mono">{colorCombination.shoes}</span>
                </div>
              </div>
            </div>

            {/* Success Indicator */}
            <div className="mt-4 pt-3 border-t border-white/20">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-300 font-medium">Colors Applied</span>
              </div>
              {recommendations && recommendations.outfits && recommendations.outfits[selectedOutfitIndex] && (
                <div className="text-center">
                  <div className="text-xs text-white/80 font-medium">
                    {recommendations.outfits[selectedOutfitIndex].outfitName}
                  </div>
                  <div className="text-xs text-white/60 mt-1">
                    Outfit {selectedOutfitIndex + 1} of {recommendations.outfits.length}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Debug Info - Remove in production */}
      {colorCombination && (
        <div className="fixed top-4 left-4 z-50 bg-black/80 text-white p-2 rounded text-xs">
          <div>Colors Active:</div>
          <div>Shirt: {colorCombination.shirt}</div>
          <div>Pants: {colorCombination.pants}</div>
          <div>Shoes: {colorCombination.shoes}</div>
          <div>Model: {selectedModel}</div>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-xl flex items-center justify-center z-50">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20 text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
                <div className="absolute inset-2 border-4 border-transparent border-t-pink-500 rounded-full animate-spin animation-delay-150"></div>
                <div className="absolute inset-4 border-4 border-transparent border-t-blue-500 rounded-full animate-spin animation-delay-300"></div>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-xl font-bold text-white">Analyzing Colors</h3>
              <p className="text-purple-200">AI is crafting your perfect palette...</p>
              <div className="flex justify-center space-x-1 mt-4">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce animation-delay-100"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-30 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  );
};