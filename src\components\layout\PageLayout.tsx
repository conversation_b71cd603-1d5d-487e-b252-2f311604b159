'use client';

import React from 'react';
import { Navbar } from './Navbar';

interface PageLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  showNavbar?: boolean;
}

export const PageLayout: React.FC<PageLayoutProps> = ({ 
  children, 
  title, 
  description,
  showNavbar = true 
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Navbar */}
      {showNavbar && <Navbar />}

      {/* Page Header */}
      {(title || description) && (
        <div className="relative z-10 max-w-7xl mx-auto px-6 py-8 pt-20 lg:pt-24">
          <div className="text-center">
            {title && (
              <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-4">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-lg text-purple-200 max-w-2xl mx-auto">
                {description}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="relative z-10 pt-16 lg:pt-20 pb-24 lg:pb-8">
        {children}
      </div>
    </div>
  );
};
