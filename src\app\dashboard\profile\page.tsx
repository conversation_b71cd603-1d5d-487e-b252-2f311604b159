'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { authAPI } from '@/lib/api';
import { PageLayout } from '@/components/layout/PageLayout';
import { 
  User, 
  Mail, 
  Calendar, 
  MapPin, 
  Phone, 
  Edit3, 
  Save, 
  X, 
  Camera,
  Star,
  Palette,
  Eye,
  Settings,
  Shield,
  Bell,
  Globe
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  gender?: 'male' | 'female';
  isEmailVerified: boolean;
  lastLogin?: string;
  dateOfBirth?: string;
  location?: string;
  phone?: string;
  preferences?: {
    notifications: boolean;
    newsletter: boolean;
    publicProfile: boolean;
  };
  createdAt: string;
  updatedAt?: string;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    gender: '',
    dateOfBirth: '',
    location: '',
    phone: '',
    preferences: {
      notifications: true,
      newsletter: true,
      publicProfile: false
    }
  });

  useEffect(() => {
    console.log('Profile page mounted, user from context:', user);
    fetchProfile();
  }, []);

  // Helper function to get token from cookies
  const getTokenFromCookie = (): string | null => {
    if (typeof window === 'undefined') return null;
    const nameEQ = "authToken=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  };

  // Debug info
  const debugInfo = {
    hasToken: !!getTokenFromCookie(),
    tokenPreview: getTokenFromCookie()?.substring(0, 20) + '...',
    userFromContext: user,
    profileFromAPI: profile
  };

  const testDirectAPICall = async () => {
    try {
      const token = getTokenFromCookie();
      console.log('Testing direct API call with token:', token?.substring(0, 20) + '...');

      const response = await fetch('https://faceapp-ttwh.onrender.com/api/auth/me', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Direct API response status:', response.status);
      const data = await response.json();
      console.log('Direct API response data:', data);

      if (response.ok) {
        toast.success('Direct API call successful!');
      } else {
        toast.error(`Direct API call failed: ${data.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Direct API call error:', error);
      toast.error('Direct API call failed');
    }
  };

  const fetchProfile = async () => {
    try {
      setIsLoading(true);
      console.log('Fetching profile data...');

      // Check if token exists
      const token = getTokenFromCookie();
      console.log('Token exists:', !!token);

      const response = await authAPI.getProfile();
      console.log('Profile API response:', response);

      if (response.success && response.data) {
        console.log('Profile data received:', response.data);
        setProfile(response.data);
        setEditForm({
          name: response.data.name || '',
          gender: response.data.gender || '',
          dateOfBirth: response.data.dateOfBirth || '',
          location: response.data.location || '',
          phone: response.data.phone || '',
          preferences: {
            notifications: response.data.preferences?.notifications ?? true,
            newsletter: response.data.preferences?.newsletter ?? true,
            publicProfile: response.data.preferences?.publicProfile ?? false
          }
        });
      } else {
        console.error('Profile API failed:', response);
        toast.error(response.message || 'Failed to load profile');
      }
    } catch (error: any) {
      console.error('Failed to fetch profile:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.response?.status === 401) {
        toast.error('Authentication failed. Please login again.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load profile');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      console.log('Updating profile with data:', editForm);

      const response = await authAPI.updateProfile(editForm);
      console.log('Profile update response:', response);

      if (response.success && response.data) {
        console.log('Profile updated successfully:', response.data);
        setProfile(response.data);
        setIsEditing(false);
        toast.success('Profile updated successfully!');
      } else {
        console.error('Profile update failed:', response);
        toast.error(response.message || 'Failed to update profile');
      }
    } catch (error: any) {
      console.error('Failed to update profile:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      if (error.response?.status === 401) {
        toast.error('Authentication failed. Please login again.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to update profile');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setEditForm({
        name: profile.name || '',
        gender: profile.gender || '',
        dateOfBirth: profile.dateOfBirth || '',
        location: profile.location || '',
        phone: profile.phone || '',
        preferences: {
          notifications: profile.preferences?.notifications ?? true,
          newsletter: profile.preferences?.newsletter ?? true,
          publicProfile: profile.preferences?.publicProfile ?? false
        }
      });
    }
    setIsEditing(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-xl opacity-50 animate-pulse"></div>
              <div className="relative w-16 h-16 mx-auto">
                <div className="absolute inset-0 border-4 border-purple-500/30 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-purple-500 rounded-full animate-spin"></div>
              </div>
            </div>
            <h3 className="text-xl font-bold text-white mb-2">Loading Profile</h3>
            <p className="text-purple-200">Fetching your details...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout 
      title="Profile Settings"
      description="Manage your account information and preferences"
    >
      <div className="max-w-4xl mx-auto px-6 pb-12">
        {/* Profile Header */}
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 mb-8">
          <div className="flex flex-col md:flex-row items-center gap-6">
            {/* Avatar */}
            <div className="relative">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                <User className="w-12 h-12 text-white" />
              </div>
              <button className="absolute -bottom-2 -right-2 p-2 bg-white/20 backdrop-blur-sm rounded-full border border-white/30 hover:bg-white/30 transition-colors">
                <Camera className="w-4 h-4 text-white" />
              </button>
            </div>

            {/* User Info */}
            <div className="flex-1 text-center md:text-left">
              <h2 className="text-2xl font-bold text-white mb-2">{profile?.name}</h2>
              <div className="flex items-center gap-2 justify-center md:justify-start mb-1">
                <p className="text-purple-200">{profile?.email}</p>
                {profile?.isEmailVerified && (
                  <div className="flex items-center gap-1 px-2 py-1 bg-emerald-500/20 rounded-full">
                    <Shield className="w-3 h-3 text-emerald-400" />
                    <span className="text-xs text-emerald-400 font-medium">Verified</span>
                  </div>
                )}
              </div>
              <p className="text-purple-300 text-sm">
                Member since {profile?.createdAt ? formatDate(profile.createdAt) : 'Unknown'}
              </p>
              {profile?.lastLogin && (
                <p className="text-purple-400 text-xs mt-1">
                  Last login: {formatDate(profile.lastLogin)}
                </p>
              )}
            </div>

            {/* Edit Button */}
            <div className="flex gap-3">
              {isEditing ? (
                <>
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-2xl hover:shadow-lg transition-all duration-300 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    onClick={handleCancel}
                    className="flex items-center gap-2 px-6 py-3 bg-white/10 text-white rounded-2xl hover:bg-white/20 transition-all duration-300"
                  >
                    <X className="w-4 h-4" />
                    Cancel
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl hover:shadow-lg transition-all duration-300"
                >
                  <Edit3 className="w-4 h-4" />
                  Edit Profile
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Account Status */}
        <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 mb-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white">Account Status</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* User ID */}
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <div className="text-2xl font-bold text-white mb-1">{profile?.id}</div>
              <div className="text-purple-200 text-sm">User ID</div>
            </div>

            {/* Email Verification */}
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <div className={`text-2xl font-bold mb-1 ${profile?.isEmailVerified ? 'text-emerald-400' : 'text-red-400'}`}>
                {profile?.isEmailVerified ? 'Verified' : 'Unverified'}
              </div>
              <div className="text-purple-200 text-sm">Email Status</div>
            </div>

            {/* Gender */}
            <div className="text-center p-4 bg-white/5 rounded-xl">
              <div className="text-2xl font-bold text-white mb-1 capitalize">{profile?.gender || 'Not Set'}</div>
              <div className="text-purple-200 text-sm">Gender</div>
            </div>
          </div>

          {/* Last Login */}
          {profile?.lastLogin && (
            <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-500/20">
              <div className="flex items-center gap-3">
                <Eye className="w-5 h-5 text-blue-400" />
                <div>
                  <div className="text-white font-medium">Last Login</div>
                  <div className="text-blue-200 text-sm">{formatDate(profile.lastLogin)}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Personal Information */}
          <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl">
                <User className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white">Personal Information</h3>
            </div>

            <div className="space-y-4">
              {/* Name */}
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">Full Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.name}
                    onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400"
                    placeholder="Enter your full name"
                  />
                ) : (
                  <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                    <User className="w-4 h-4 text-purple-300" />
                    <span className="text-white">{profile?.name || 'Not provided'}</span>
                  </div>
                )}
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">Email Address</label>
                <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                  <Mail className="w-4 h-4 text-purple-300" />
                  <span className="text-white">{profile?.email}</span>
                </div>
              </div>

              {/* Gender */}
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">Gender</label>
                {isEditing ? (
                  <select
                    value={editForm.gender}
                    onChange={(e) => setEditForm({ ...editForm, gender: e.target.value })}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-purple-400"
                  >
                    <option value="">Select gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                ) : (
                  <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                    <User className="w-4 h-4 text-purple-300" />
                    <span className="text-white capitalize">{profile?.gender || 'Not specified'}</span>
                  </div>
                )}
              </div>

              {/* Date of Birth */}
              <div>
                <label className="block text-sm font-medium text-purple-200 mb-2">Date of Birth</label>
                {isEditing ? (
                  <input
                    type="date"
                    value={editForm.dateOfBirth}
                    onChange={(e) => setEditForm({ ...editForm, dateOfBirth: e.target.value })}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:border-purple-400"
                  />
                ) : (
                  <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                    <Calendar className="w-4 h-4 text-purple-300" />
                    <span className="text-white">
                      {profile?.dateOfBirth ? formatDate(profile.dateOfBirth) : 'Not provided'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Contact & Preferences */}
          <div className="space-y-8">
            {/* Contact Information */}
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl">
                  <Phone className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">Contact Information</h3>
              </div>

              <div className="space-y-4">
                {/* Location */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">Location</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.location}
                      onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400"
                      placeholder="City, Country"
                    />
                  ) : (
                    <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                      <MapPin className="w-4 h-4 text-purple-300" />
                      <span className="text-white">{profile?.location || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-purple-200 mb-2">Phone Number</label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={editForm.phone}
                      onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-purple-300 focus:outline-none focus:border-purple-400"
                      placeholder="+****************"
                    />
                  ) : (
                    <div className="flex items-center gap-3 px-4 py-3 bg-white/5 rounded-xl">
                      <Phone className="w-4 h-4 text-purple-300" />
                      <span className="text-white">{profile?.phone || 'Not provided'}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Preferences */}
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl">
                  <Settings className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white">Preferences</h3>
              </div>

              <div className="space-y-4">
                {/* Notifications */}
                <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Bell className="w-4 h-4 text-purple-300" />
                    <div>
                      <p className="text-white font-medium">Notifications</p>
                      <p className="text-purple-200 text-sm">Receive analysis updates</p>
                    </div>
                  </div>
                  {isEditing ? (
                    <input
                      type="checkbox"
                      checked={editForm.preferences.notifications}
                      onChange={(e) => setEditForm({
                        ...editForm,
                        preferences: { ...editForm.preferences, notifications: e.target.checked }
                      })}
                      className="w-5 h-5 text-purple-500 rounded focus:ring-purple-400"
                    />
                  ) : (
                    <div className={`w-5 h-5 rounded ${profile?.preferences?.notifications ? 'bg-emerald-500' : 'bg-gray-400'}`} />
                  )}
                </div>

                {/* Newsletter */}
                <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Mail className="w-4 h-4 text-purple-300" />
                    <div>
                      <p className="text-white font-medium">Newsletter</p>
                      <p className="text-purple-200 text-sm">Style tips and updates</p>
                    </div>
                  </div>
                  {isEditing ? (
                    <input
                      type="checkbox"
                      checked={editForm.preferences.newsletter}
                      onChange={(e) => setEditForm({
                        ...editForm,
                        preferences: { ...editForm.preferences, newsletter: e.target.checked }
                      })}
                      className="w-5 h-5 text-purple-500 rounded focus:ring-purple-400"
                    />
                  ) : (
                    <div className={`w-5 h-5 rounded ${profile?.preferences?.newsletter ? 'bg-emerald-500' : 'bg-gray-400'}`} />
                  )}
                </div>

                {/* Public Profile */}
                <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                  <div className="flex items-center gap-3">
                    <Globe className="w-4 h-4 text-purple-300" />
                    <div>
                      <p className="text-white font-medium">Public Profile</p>
                      <p className="text-purple-200 text-sm">Make profile visible to others</p>
                    </div>
                  </div>
                  {isEditing ? (
                    <input
                      type="checkbox"
                      checked={editForm.preferences.publicProfile}
                      onChange={(e) => setEditForm({
                        ...editForm,
                        preferences: { ...editForm.preferences, publicProfile: e.target.checked }
                      })}
                      className="w-5 h-5 text-purple-500 rounded focus:ring-purple-400"
                    />
                  ) : (
                    <div className={`w-5 h-5 rounded ${profile?.preferences?.publicProfile ? 'bg-emerald-500' : 'bg-gray-400'}`} />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Debug Information (remove in production) */}
        <div className="mt-8 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl">
              <Settings className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-bold text-white">Debug Information</h3>
          </div>
          <div className="space-y-2 text-sm">
            <div className="p-3 bg-white/5 rounded-xl">
              <span className="text-purple-200">Has Token: </span>
              <span className="text-white">{debugInfo.hasToken ? 'Yes' : 'No'}</span>
            </div>
            <div className="p-3 bg-white/5 rounded-xl">
              <span className="text-purple-200">Token Preview: </span>
              <span className="text-white font-mono text-xs">{debugInfo.tokenPreview || 'None'}</span>
            </div>
            <div className="p-3 bg-white/5 rounded-xl">
              <span className="text-purple-200">User from Context: </span>
              <span className="text-white">{debugInfo.userFromContext?.name || 'None'}</span>
            </div>
            <div className="p-3 bg-white/5 rounded-xl">
              <span className="text-purple-200">Profile from API: </span>
              <span className="text-white">{debugInfo.profileFromAPI?.name || 'None'}</span>
            </div>
            <div className="p-3 bg-white/5 rounded-xl">
              <span className="text-purple-200">Loading State: </span>
              <span className="text-white">{isLoading ? 'Loading...' : 'Loaded'}</span>
            </div>
          </div>
          <div className="mt-4 flex gap-3">
            <button
              onClick={fetchProfile}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl hover:shadow-lg transition-all duration-300"
            >
              Refresh Profile Data
            </button>
            <button
              onClick={testDirectAPICall}
              className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:shadow-lg transition-all duration-300"
            >
              Test Direct API Call
            </button>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
