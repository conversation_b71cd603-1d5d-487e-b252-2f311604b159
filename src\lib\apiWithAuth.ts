import axios from 'axios';
import { ApiResponse, ColorRecommendation } from '@/types';

const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// Create a function that returns an API instance with auth headers
export const createAuthenticatedAPI = (authHeaders: { Authorization?: string }) => {
  const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
      ...authHeaders,
    },
  });

  // Response interceptor for error handling
  api.interceptors.response.use(
    (response) => {
      console.log('Authenticated API Response:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      });
      return response;
    },
    (error) => {
      console.error('Authenticated API Error:', {
        url: error.config?.url,
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      return Promise.reject(error);
    }
  );

  return {
    // Color Recommendation API functions with auth
    getLatestRecommendation: async (): Promise<ApiResponse<ColorRecommendation>> => {
      const response = await api.get('/face/recommendations/latest');
      return response.data;
    },

    getRecommendations: async (
      analysisId: string,
      preferences: { style?: string; occasion?: string }
    ): Promise<ApiResponse<ColorRecommendation>> => {
      const response = await api.post(`/face/analysis/${analysisId}/recommendations`, { preferences });
      return response.data;
    },

    getRecommendationHistory: async (limit = 10): Promise<ApiResponse<ColorRecommendation[]>> => {
      const response = await api.get(`/face/recommendations/history?limit=${limit}`);
      return response.data;
    },
  };
};
