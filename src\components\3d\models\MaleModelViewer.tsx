'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useGLTF } from '@react-three/drei';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

interface ColorCombination {
  id: string;
  shirt: string;
  pants: string;
  shoes: string;
  outfitName: string;
  overallReason: string;
}

// 3D Model Component
const Model3D: React.FC<{ selectedCombination: ColorCombination | null }> = ({ selectedCombination }) => {
  const group = useRef<THREE.Group>(null);
  const { nodes, materials } = useGLTF('/temp.glb') as any;
  
  // Refs for direct material access
  const sweaterRef = useRef<THREE.SkinnedMesh>(null);
  const collarRef = useRef<THREE.SkinnedMesh>(null);
  const pantsRef = useRef<THREE.SkinnedMesh>(null);
  const shoesRef = useRef<THREE.SkinnedMesh>(null);

  // Apply colors when selected combination changes
  useEffect(() => {
    if (!selectedCombination) return;

    console.log('🎨 Applying colors to model:', selectedCombination);

    // Apply colors to each mesh directly
    if (sweaterRef.current?.material) {
      const sweaterMat = sweaterRef.current.material as THREE.MeshStandardMaterial;
      if (sweaterMat.color) {
        sweaterMat.color.set(selectedCombination.shirt);
        sweaterMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${selectedCombination.shirt} to sweater`);
      }
    }

    if (collarRef.current?.material) {
      const collarMat = collarRef.current.material as THREE.MeshStandardMaterial;
      if (collarMat.color) {
        collarMat.color.set(selectedCombination.shirt);
        collarMat.needsUpdate = true;
        console.log(`✅ Applied shirt color ${selectedCombination.shirt} to collar`);
      }
    }

    if (pantsRef.current?.material) {
      const pantsMat = pantsRef.current.material as THREE.MeshStandardMaterial;
      if (pantsMat.color) {
        pantsMat.color.set(selectedCombination.pants);
        pantsMat.needsUpdate = true;
        console.log(`✅ Applied pants color ${selectedCombination.pants} to pants`);
      }
    }

    if (shoesRef.current?.material) {
      const shoesMat = shoesRef.current.material as THREE.MeshStandardMaterial;
      if (shoesMat.color) {
        shoesMat.color.set(selectedCombination.shoes);
        shoesMat.needsUpdate = true;
        console.log(`✅ Applied shoes color ${selectedCombination.shoes} to shoes`);
      }
    }
  }, [selectedCombination]);

  if (!nodes || !materials) {
    console.log('⏳ Model not loaded yet...');
    return null;
  }

  return (
    <group ref={group} dispose={null} scale={[0.01, 0.01, 0.01]} position={[0, -1, 0]} rotation={[Math.PI / 2, 0, 0]}>
      <group name="Scene">
        <group name="Armature">
          {/* Body - Keep original skin color */}
          <skinnedMesh
            name="Ch31_Body"
            geometry={nodes.Ch31_Body?.geometry}
            material={materials.Ch31_body}
            skeleton={nodes.Ch31_Body?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Collar - Apply shirt color */}
          <skinnedMesh
            ref={collarRef}
            name="Ch31_Collar"
            geometry={nodes.Ch31_Collar?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Collar?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Eyelashes - Keep hair color */}
          <skinnedMesh
            name="Ch31_Eyelashes"
            geometry={nodes.Ch31_Eyelashes?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Eyelashes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Hair - Keep original hair color */}
          <skinnedMesh
            name="Ch31_Hair"
            geometry={nodes.Ch31_Hair?.geometry}
            material={materials.Ch31_hair}
            skeleton={nodes.Ch31_Hair?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Pants - Apply pants color */}
          <skinnedMesh
            ref={pantsRef}
            name="Ch31_Pants"
            geometry={nodes.Ch31_Pants?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Pants?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Shoes - Apply shoes color */}
          <skinnedMesh
            ref={shoesRef}
            name="Ch31_Shoes"
            geometry={nodes.Ch31_Shoes?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Shoes?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Sweater - Apply shirt color */}
          <skinnedMesh
            ref={sweaterRef}
            name="Ch31_Sweater"
            geometry={nodes.Ch31_Sweater?.geometry}
            material={materials.Ch31_body?.clone()}
            skeleton={nodes.Ch31_Sweater?.skeleton}
            castShadow
            receiveShadow
          />

          {/* Armature */}
          <primitive object={nodes.mixamorig9Hips} />
        </group>
      </group>
    </group>
  );
};

// Main Component
export const MaleModelViewer: React.FC = () => {
  const [colorCombinations, setColorCombinations] = useState<ColorCombination[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load color recommendations when component mounts
  useEffect(() => {
    loadColorRecommendations();
  }, []);

  const loadColorRecommendations = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('🎨 Loading color recommendations from API...');
      
      const response = await fetch('https://faceapp-ttwh.onrender.com/api/face/recommendations/latest', {
        method: 'GET',
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ API Response:', data);

      if (data && data.recommendations && data.recommendations.length > 0) {
        // Convert API response to our format
        const combinations: ColorCombination[] = data.recommendations.map((outfit: any, index: number) => ({
          id: `outfit-${index}`,
          shirt: outfit.shirt?.hex || '#3498db',
          pants: outfit.pants?.hex || '#2c3e50',
          shoes: outfit.shoes?.hex || '#000000',
          outfitName: outfit.outfitName || `Style ${index + 1}`,
          overallReason: outfit.overallReason || 'AI recommended combination'
        }));

        setColorCombinations(combinations);
        console.log('🎨 Converted combinations:', combinations);
      } else {
        setError('No color recommendations found');
        console.log('⚠️ No recommendations in response');
      }
    } catch (err: any) {
      console.error('❌ Error loading recommendations:', err);
      setError(err.message || 'Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedCombination = colorCombinations[selectedIndex] || null;

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-purple-900 via-indigo-900 to-blue-900 relative">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 p-6">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
            🎨 AI Color Recommendations
          </h1>
          <p className="text-purple-200">
            Click on color combinations to see them applied to the 3D model
          </p>
        </div>
      </div>

      {/* 3D Canvas */}
      <div className="w-full h-screen">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%' }}
          gl={{ antialias: true, alpha: true }}
          dpr={[1, 2]}
        >
          <ambientLight intensity={0.6} />
          <directionalLight position={[10, 10, 5]} intensity={1} />
          <Environment preset="studio" />

          <Model3D selectedCombination={selectedCombination} />

          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={2}
            maxDistance={10}
          />
        </Canvas>
      </div>

      {/* Color Combinations Panel */}
      <div className="absolute right-0 top-0 bottom-0 w-full md:w-96 bg-black/40 backdrop-blur-2xl border-l border-white/10 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
              <span className="text-2xl">🎨</span>
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">Color Combinations</h3>
              <p className="text-purple-200 text-sm">
                {colorCombinations.length} AI-generated outfits
              </p>
            </div>
          </div>

          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-white">Loading recommendations...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-400 mb-4">{error}</p>
              <button
                onClick={loadColorRecommendations}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Retry
              </button>
            </div>
          )}

          {colorCombinations.length > 0 && (
            <div className="space-y-4">
              {colorCombinations.map((combination, index) => (
                <div
                  key={combination.id}
                  onClick={() => setSelectedIndex(index)}
                  className={`p-4 rounded-2xl cursor-pointer transition-all duration-300 ${
                    selectedIndex === index
                      ? 'bg-gradient-to-r from-purple-500/30 to-pink-500/30 border-2 border-purple-400'
                      : 'bg-white/10 hover:bg-white/20 border-2 border-transparent'
                  }`}
                >
                  <h4 className="text-white font-semibold mb-3">{combination.outfitName}</h4>
                  
                  {/* Color Preview */}
                  <div className="flex gap-3 mb-3">
                    <div className="flex flex-col items-center">
                      <div
                        className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                        style={{ backgroundColor: combination.shirt }}
                      ></div>
                      <span className="text-xs text-white/70 mt-1">Shirt</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div
                        className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                        style={{ backgroundColor: combination.pants }}
                      ></div>
                      <span className="text-xs text-white/70 mt-1">Pants</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div
                        className="w-12 h-12 rounded-xl border-2 border-white/30 shadow-lg"
                        style={{ backgroundColor: combination.shoes }}
                      ></div>
                      <span className="text-xs text-white/70 mt-1">Shoes</span>
                    </div>
                  </div>

                  <p className="text-white/80 text-sm">{combination.overallReason}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Preload the model
useGLTF.preload('/temp.glb');
