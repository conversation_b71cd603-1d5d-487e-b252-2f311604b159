'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, LoadingState } from '@/types';
import { authAPI } from '@/lib/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: LoadingState;
  login: (email: string, password: string, onSuccess?: () => void) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string, gender: string) => Promise<boolean>;
  verifyOTP: (email: string, otp: string) => Promise<boolean>;
  resendOTP: (email: string) => Promise<boolean>;
  isAuthenticated: boolean;
  refreshUser: () => Promise<void>;
  getAuthHeaders: () => { Authorization?: string };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState<LoadingState>({ isLoading: true });



  // Check if user is authenticated on mount
  useEffect(() => {
    console.log('AuthProvider: Initializing, checking auth status...');
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    // For now, we'll start with no authentication check
    // In a real app, you might check for a session cookie or other persistent auth
    console.log('AuthProvider: Starting without persistent authentication');
    setLoading({ isLoading: false });
  };

  const login = async (email: string, password: string, onSuccess?: () => void): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Signing in...' });

      console.log('AuthContext: Starting login process for:', email);
      const response = await authAPI.login({ email, password });
      console.log('AuthContext: Login API response:', response);

      if (response.success) {
        // Handle both response formats: direct token/user or nested in data
        let token, user;

        if (response.token && response.user) {
          // Direct format (as per API docs and our test)
          token = response.token;
          user = response.user;
          console.log('AuthContext: Using direct format from API response');
        } else if (response.data && response.data.token && response.data.user) {
          // Nested format (fallback)
          token = response.data.token;
          user = response.data.user;
          console.log('AuthContext: Using nested data format');
        } else {
          console.error('AuthContext: Invalid response format - no token/user found');
          console.error('AuthContext: Response structure:', response);
          toast.error('Invalid login response format');
          return false;
        }

        console.log('AuthContext: Login successful, storing token and user:', {
          tokenLength: token?.length,
          tokenStart: token?.substring(0, 20) + '...',
          user
        });

        // Validate token and user data
        if (!token || !user) {
          console.error('AuthContext: Invalid response data - missing token or user');
          toast.error('Invalid login response');
          return false;
        }

        // Store token in memory (not localStorage)
        setToken(token);
        console.log('AuthContext: Token stored in memory');

        // Set user in context
        setUser(user);
        console.log('AuthContext: User set in context, isAuthenticated will be:', !!user);

        toast.success('Login successful!');

        // Call success callback after a short delay to ensure state is updated
        if (onSuccess) {
          setTimeout(() => {
            console.log('AuthContext: Calling onSuccess callback');
            onSuccess();
          }, 100);
        }

        return true;
      } else {
        console.error('AuthContext: Login failed - API response:', response);
        toast.error(response.message || 'Login failed');
        return false;
      }
    } catch (error: any) {
      console.error('AuthContext: Login error:', error);
      console.error('AuthContext: Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      const message = error.response?.data?.message || error.message || 'Login failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
      console.log('AuthContext: Login process completed, loading set to false');
    }
  };

  const register = async (
    name: string,
    email: string,
    password: string,
    gender: string
  ): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Creating account...' });
      const response = await authAPI.register({
        name,
        email,
        password,
        gender: gender as any,
      });
      
      if (response.success) {
        toast.success(response.message || 'Registration successful! Please check your email for OTP.');
        return true;
      } else {
        toast.error(response.message || 'Registration failed');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const verifyOTP = async (email: string, otp: string): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Verifying OTP...' });
      const response = await authAPI.verifyOTP({ email, otp });
      
      if (response.success) {
        toast.success('Email verified successfully!');
        return true;
      } else {
        toast.error(response.message || 'OTP verification failed');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'OTP verification failed';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const resendOTP = async (email: string): Promise<boolean> => {
    try {
      setLoading({ isLoading: true, message: 'Resending OTP...' });
      const response = await authAPI.resendOTP(email);
      
      if (response.success) {
        toast.success(response.message || 'OTP sent successfully!');
        return true;
      } else {
        toast.error(response.message || 'Failed to resend OTP');
        return false;
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to resend OTP';
      toast.error(message);
      return false;
    } finally {
      setLoading({ isLoading: false });
    }
  };

  const logout = async () => {
    try {
      setLoading({ isLoading: true, message: 'Signing out...' });
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setToken(null);
      setUser(null);
      setLoading({ isLoading: false });
      toast.success('Logged out successfully');
    }
  };

  const refreshUser = async () => {
    try {
      const response = await authAPI.getProfile();
      if (response.success && response.data) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    return token ? { Authorization: `Bearer ${token}` } : {};
  };

  const value: AuthContextType = {
    user,
    token,
    loading,
    login,
    logout,
    register,
    verifyOTP,
    resendOTP,
    isAuthenticated: !!user && !!token,
    refreshUser,
    getAuthHeaders,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
