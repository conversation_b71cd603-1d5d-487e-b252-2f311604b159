import axios, { AxiosResponse, AxiosError } from 'axios';
import {
  ApiResponse,
  LoginResponse,
  RegisterData,
  LoginData,
  OTPData,
  User,
  FaceAnalysis,
  AnalysisRequest,
  ColorRecommendation,
  RecommendationRequest,
  UploadSignature,
  FeedbackData,
} from '@/types';

const API_BASE_URL = 'https://faceapp-ttwh.onrender.com/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper function to get token from cookies
const getTokenFromCookie = (): string | null => {
  if (typeof window === 'undefined') return null;
  const nameEQ = "authToken=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
};

// Request interceptor to add auth token from cookies
api.interceptors.request.use((config) => {
  const token = getTokenFromCookie();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    console.log('API: Added Bearer token to request:', config.url);
  }
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log('API Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error: AxiosError<ApiResponse>) => {
    console.error('API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    if (error.response?.status === 401) {
      // Token expired or invalid - redirect to auth
      window.location.href = '/auth';
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  register: async (data: RegisterData): Promise<ApiResponse<{ user: User }>> => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  verifyOTP: async (data: OTPData): Promise<ApiResponse> => {
    const response = await api.post('/auth/verify-email-otp', data);
    return response.data;
  },

  login: async (data: LoginData): Promise<LoginResponse> => {
    console.log('API: Sending login request with data:', { email: data.email, password: '***' });
    const response = await api.post('/auth/login', data);
    console.log('API: Raw login response:', response.data);

    const responseData = response.data;

    // Based on our test, the API returns token and user directly in the response
    if (responseData.success && responseData.token && responseData.user) {
      console.log('API: Login successful - token and user found directly in response');
      // Return the response as-is but also add to data property for compatibility
      return {
        success: responseData.success,
        message: responseData.message || 'Login successful',
        token: responseData.token,  // Keep direct access
        user: responseData.user,    // Keep direct access
        data: {
          token: responseData.token,
          user: responseData.user
        }
      };
    }

    // Fallback: check if data is nested (shouldn't happen based on our test)
    if (responseData.success && responseData.data?.token && responseData.data?.user) {
      console.log('API: Login successful - token and user found in nested data');
      return responseData;
    }

    // If login failed
    console.error('API: Login failed:', responseData);
    return {
      success: false,
      message: responseData?.message || 'Login failed',
      data: undefined
    };
  },

  getProfile: async (): Promise<ApiResponse<User>> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  updateProfile: async (profileData: any): Promise<ApiResponse<User>> => {
    const response = await api.put('/auth/update-profile', profileData);
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  resendOTP: async (email: string): Promise<ApiResponse> => {
    const response = await api.post('/auth/resend-verification', { email });
    return response.data;
  },
};

// Face Analysis API functions
export const faceAPI = {
  // New URL-based analysis (recommended)
  analyzeFaceFromUrl: async (imageUrl: string, originalFileName?: string): Promise<ApiResponse<FaceAnalysis>> => {
    console.log('API: Analyzing face from URL:', imageUrl);
    const response = await api.post('/face/analyze-url', {
      imageUrl,
      originalFileName: originalFileName || 'image.jpg'
    });
    console.log('API: Face analysis response:', response.data);
    return response.data;
  },

  // Legacy direct analysis (fallback)
  analyzeFace: async (data: AnalysisRequest): Promise<ApiResponse<{ analysis: FaceAnalysis }>> => {
    console.log('API: Analyzing face with direct method:', data);
    const response = await api.post('/face/analyze-direct', data);
    console.log('API: Direct analysis response:', response.data);
    return response.data;
  },

  getHistory: async (page = 1, limit = 10): Promise<ApiResponse<{ analyses: FaceAnalysis[] }>> => {
    const response = await api.get(`/face/history?page=${page}&limit=${limit}`);
    return response.data;
  },

  getAnalysis: async (id: string): Promise<ApiResponse<FaceAnalysis>> => {
    const response = await api.get(`/face/analysis/${id}`);
    return response.data;
  },

  deleteAnalysis: async (id: string): Promise<ApiResponse> => {
    const response = await api.delete(`/face/analysis/${id}`);
    return response.data;
  },
};

// Color Recommendation API functions
export const recommendationAPI = {
  getRecommendations: async (
    analysisId: string,
    preferences: RecommendationRequest['preferences']
  ): Promise<ApiResponse<ColorRecommendation>> => {
    const response = await api.post(`/face/analysis/${analysisId}/recommendations`, { preferences });
    return response.data;
  },

  getLatestRecommendation: async (): Promise<ApiResponse<ColorRecommendation>> => {
    const response = await api.get('/face/recommendations/latest');
    return response.data;
  },

  getRecommendationHistory: async (limit = 10): Promise<ApiResponse<ColorRecommendation[]>> => {
    const response = await api.get(`/face/recommendations/history?limit=${limit}`);
    return response.data;
  },

  addFeedback: async (
    recommendationId: string,
    feedback: FeedbackData
  ): Promise<ApiResponse> => {
    const response = await api.post(`/face/recommendations/${recommendationId}/feedback`, feedback);
    return response.data;
  },
};

// Upload API functions
export const uploadAPI = {
  // Get upload signature for web (not mobile)
  getUploadSignature: async (): Promise<ApiResponse<UploadSignature>> => {
    console.log('API: Requesting web upload signature...');
    const response = await api.post('/upload/signature');
    console.log('API: Upload signature response:', response.data);

    const responseData = response.data;

    // Handle the new API response format with formData
    if (responseData.success && responseData.data) {
      console.log('API: Web upload signature obtained successfully');
      const data = responseData.data;

      return {
        success: true,
        message: 'Upload signature generated',
        data: {
          signature: data.signature,
          timestamp: data.timestamp,
          cloudName: data.cloudName,
          apiKey: data.apiKey,
          uploadUrl: data.uploadUrl,
          folder: data.formData?.folder,
          public_id: data.formData?.public_id,
          context: data.formData?.context,
          tags: data.formData?.tags,
          transformation: data.formData?.transformation,
          allowed_formats: data.formData?.allowed_formats,
          // Include the complete formData for easy access
          formData: data.formData
        }
      };
    }

    console.error('API: Failed to get web upload signature:', responseData);
    return {
      success: false,
      message: responseData?.message || 'Failed to get upload signature',
      data: undefined
    };
  },

  // Fallback: Try mobile signature endpoint
  getMobileUploadSignature: async (): Promise<ApiResponse<UploadSignature>> => {
    console.log('API: Requesting mobile upload signature as fallback...');
    const response = await api.post('/upload/mobile-signature');
    console.log('API: Mobile signature response:', response.data);

    const responseData = response.data;

    if (responseData.success && responseData.signature) {
      return {
        success: true,
        message: 'Upload signature generated',
        data: {
          signature: responseData.signature,
          timestamp: responseData.timestamp,
          cloudName: responseData.cloud_name,
          apiKey: responseData.api_key,
          uploadUrl: responseData.upload_url,
          folder: responseData.folder,
          public_id: responseData.public_id,
          context: responseData.context,
          tags: responseData.tags,
          transformation: responseData.transformation,
          allowed_formats: responseData.allowed_formats
        }
      };
    }

    return {
      success: false,
      message: responseData?.message || 'Failed to get mobile signature',
      data: undefined
    };
  },

  getUploadConfig: async (): Promise<ApiResponse> => {
    const response = await api.get('/upload/config');
    return response.data;
  },

  // Direct upload through backend
  uploadImage: async (file: File): Promise<ApiResponse<{ imageUrl: string; publicId: string }>> => {
    console.log('API: Uploading image through backend...');
    const formData = new FormData();
    formData.append('image', file);

    const response = await api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('API: Backend upload response:', response.data);
    return response.data;
  },

  // Simple Cloudinary upload with proper form data
  uploadToCloudinary: async (file: File, signature: any): Promise<any> => {
    console.log('API: Direct Cloudinary upload...');
    console.log('API: Signature data:', signature);

    const formData = new FormData();
    formData.append('file', file);

    // Use formData from signature if available (web signature format)
    if (signature.formData) {
      console.log('API: Using formData from web signature');
      Object.keys(signature.formData).forEach(key => {
        formData.append(key, signature.formData[key].toString());
      });
    } else {
      // Fallback to individual parameters (mobile signature format)
      console.log('API: Using individual parameters from mobile signature');
      formData.append('api_key', signature.apiKey);
      formData.append('timestamp', signature.timestamp.toString());
      formData.append('signature', signature.signature);

      if (signature.folder) formData.append('folder', signature.folder);
      if (signature.public_id) formData.append('public_id', signature.public_id);
      if (signature.transformation) formData.append('transformation', signature.transformation);
      if (signature.context) formData.append('context', signature.context);
      if (signature.tags) formData.append('tags', signature.tags);
      if (signature.allowed_formats) formData.append('allowed_formats', signature.allowed_formats);
    }

    const uploadUrl = signature.uploadUrl || `https://api.cloudinary.com/v1_1/${signature.cloudName}/image/upload`;
    console.log('API: Uploading to:', uploadUrl);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    console.log('API: Upload response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API: Upload failed:', errorText);
      throw new Error(`Cloudinary upload failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('API: Upload successful:', result);
    return result;
  },
};

// Health check
export const healthAPI = {
  check: async (): Promise<ApiResponse> => {
    const response = await axios.get(`${API_BASE_URL}/health`);
    return response.data;
  },

  ping: async (): Promise<string> => {
    const response = await axios.get(`${API_BASE_URL}/ping`);
    return response.data;
  },
};

export default api;
